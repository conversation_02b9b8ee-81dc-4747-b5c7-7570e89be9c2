<?php

namespace App\Http\Controllers;

use App\Models\Page;
use App\Helpers\SeoHelper;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\Response;

class PageController extends Controller
{
    /**
     * Display the specified page.
     */
    public function show(string $slug): View
    {
        $page = Page::where('slug', $slug)
                   ->published()
                   ->firstOrFail();

        // Generate SEO data
        $metaTags = SeoHelper::generateMetaTags($page);
        $structuredData = SeoHelper::generateStructuredData($page);

        // Check if page has a custom template
        if ($page->hasCustomTemplate()) {
            $templateView = $page->template_view;

            // Check if custom template exists
            if (view()->exists($templateView)) {
                return view($templateView, compact('page', 'metaTags', 'structuredData'));
            }
        }

        // Use default page template
        return view('pages.show', compact('page', 'metaTags', 'structuredData'));
    }

    /**
     * Get pages for menu display.
     */
    public function getMenuPages()
    {
        return Page::published()
                  ->forMenu()
                  ->get(['title', 'slug', 'menu_order']);
    }

    /**
     * Search pages by title or content.
     */
    public function search(Request $request): View
    {
        $query = $request->get('q');

        if (empty($query)) {
            return redirect()->route('home');
        }

        $pages = Page::published()
                    ->where(function ($q) use ($query) {
                        $q->where('title', 'like', "%{$query}%")
                          ->orWhere('content', 'like', "%{$query}%")
                          ->orWhere('excerpt', 'like', "%{$query}%");
                    })
                    ->orderBy('title')
                    ->paginate(10);

        return view('pages.search-results', compact('pages', 'query'));
    }

    /**
     * Get sitemap data for pages.
     */
    public function getSitemapData()
    {
        return SeoHelper::generateSitemapData();
    }
}
