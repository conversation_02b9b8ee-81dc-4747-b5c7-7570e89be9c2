<?php

namespace App\Services;

use App\Models\Order;
use App\Mail\OrderStatusUpdate;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;

class OrderNotificationService
{
    protected $twilioService;

    public function __construct(TwilioService $twilioService)
    {
        $this->twilioService = $twilioService;
    }

    /**
     * Send order status update notifications via SMS and Email
     */
    public function sendOrderStatusUpdate(Order $order, $status, $trackingNumber = null)
    {
        $results = [
            'sms' => ['success' => false, 'message' => 'Not attempted'],
            'email' => ['success' => false, 'message' => 'Not attempted']
        ];

        // Send SMS notification
        if ($order->user && $order->user->phone) {
            try {
                $smsResult = $this->twilioService->sendOrderStatusUpdate(
                    $order->user->phone,
                    $order->order_number,
                    $status,
                    $order->user->name,
                    $trackingNumber
                );
                $results['sms'] = $smsResult;
            } catch (\Exception $e) {
                Log::error('Failed to send order status SMS', [
                    'order_id' => $order->id,
                    'status' => $status,
                    'error' => $e->getMessage()
                ]);
                $results['sms'] = [
                    'success' => false,
                    'message' => 'SMS sending failed: ' . $e->getMessage()
                ];
            }
        }

        // Send Email notification
        if ($order->user && $order->user->email) {
            try {
                Mail::to($order->user->email)->send(new OrderStatusUpdate($order, $status, $trackingNumber));
                $results['email'] = [
                    'success' => true,
                    'message' => 'Email sent successfully'
                ];
            } catch (\Exception $e) {
                Log::error('Failed to send order status email', [
                    'order_id' => $order->id,
                    'status' => $status,
                    'error' => $e->getMessage()
                ]);
                $results['email'] = [
                    'success' => false,
                    'message' => 'Email sending failed: ' . $e->getMessage()
                ];
            }
        }

        // Log the notification attempt
        Log::info('Order status notification sent', [
            'order_id' => $order->id,
            'order_number' => $order->order_number,
            'status' => $status,
            'tracking_number' => $trackingNumber,
            'sms_success' => $results['sms']['success'],
            'email_success' => $results['email']['success']
        ]);

        return $results;
    }

    /**
     * Send order confirmation notifications (for new orders)
     */
    public function sendOrderConfirmation(Order $order)
    {
        return $this->sendOrderStatusUpdate($order, 'confirmed');
    }

    /**
     * Send bulk status updates for multiple orders
     */
    public function sendBulkStatusUpdates(array $orderIds, $status, $trackingNumbers = [])
    {
        $results = [];
        
        foreach ($orderIds as $orderId) {
            try {
                $order = Order::findOrFail($orderId);
                $trackingNumber = $trackingNumbers[$orderId] ?? null;
                
                $result = $this->sendOrderStatusUpdate($order, $status, $trackingNumber);
                $results[$orderId] = $result;
                
                // Update order status in database
                $order->update(['status' => $status]);
                if ($trackingNumber) {
                    $order->update(['tracking_number' => $trackingNumber]);
                }
                
            } catch (\Exception $e) {
                Log::error('Bulk status update failed for order', [
                    'order_id' => $orderId,
                    'status' => $status,
                    'error' => $e->getMessage()
                ]);
                
                $results[$orderId] = [
                    'sms' => ['success' => false, 'message' => 'Order not found or error occurred'],
                    'email' => ['success' => false, 'message' => 'Order not found or error occurred']
                ];
            }
        }
        
        return $results;
    }

    /**
     * Get available order statuses for notifications
     */
    public function getAvailableStatuses()
    {
        return [
            'confirmed' => 'Order Confirmed',
            'processing' => 'Being Processed',
            'shipped' => 'Shipped',
            'delivered' => 'Delivered',
            'cancelled' => 'Cancelled'
        ];
    }

    /**
     * Check if notifications are properly configured
     */
    public function checkConfiguration()
    {
        return [
            'sms_configured' => $this->twilioService->isConfigured(),
            'email_configured' => !empty(config('mail.from.address')),
            'app_name' => config('app.name'),
            'app_url' => config('app.url')
        ];
    }
}
