<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Session;
use App\Models\User;
use App\Models\OtpVerification;
use App\Models\CartItem;
use App\Services\TwilioService;

class MobileAuthController extends Controller
{
    protected $twilioService;

    public function __construct(TwilioService $twilioService)
    {
        $this->twilioService = $twilioService;
    }

    /**
     * Show mobile login form
     */
    public function showMobileLogin()
    {
        if (Auth::check()) {
            return redirect()->route('dashboard');
        }

        return view('auth.mobile-login');
    }

    /**
     * Send OTP to mobile number
     */
    public function sendOtp(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|string|regex:/^[0-9]{10}$/',
            'name' => 'required|string|min:2|max:255',
            'purpose' => 'in:login,checkout|nullable'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Please check your input.',
                'errors' => $validator->errors()
            ], 422);
        }

        $phone = $request->phone;
        $name = $request->name;
        $purpose = $request->purpose ?? 'login';
        $sessionId = session()->getId();
        $ipAddress = $request->ip();

        try {
            // Generate and send OTP
            $result = $this->twilioService->generateAndSendOtp($phone, $purpose, $sessionId, $ipAddress);

            if ($result['success']) {
                // Store phone and name in session for verification
                session([
                    'otp_phone' => $phone,
                    'otp_purpose' => $purpose,
                    'user_name' => $name
                ]);

                return response()->json([
                    'success' => true,
                    'message' => $result['message'],
                    'phone' => $phone,
                    'name' => $name,
                    'expires_at' => $result['expires_at']->format('Y-m-d H:i:s')
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $result['message']
                ], 429);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send OTP. Please try again.'
            ], 500);
        }
    }

    /**
     * Verify OTP and login/register user
     */
    public function verifyOtp(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'phone' => 'required|string|regex:/^[0-9]{10}$/',
            'otp_code' => 'required|string|size:6',
            'purpose' => 'in:login,checkout|nullable',
            'name' => 'nullable|string|max:255'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Please check your input.',
                'errors' => $validator->errors()
            ], 422);
        }

        $phone = $request->phone;
        $otpCode = $request->otp_code;
        $purpose = $request->purpose ?? 'login';
        $name = $request->name ?? session('user_name'); // Get name from request or session

        // Verify session phone matches
        if (session('otp_phone') !== $phone) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid session. Please request a new OTP.'
            ], 400);
        }

        try {
            // Verify OTP
            $otpResult = OtpVerification::verifyOtp($phone, $otpCode, $purpose);

            if (!$otpResult['success']) {
                return response()->json([
                    'success' => false,
                    'message' => $otpResult['message'],
                    'attempts_left' => $otpResult['attempts_left'] ?? null
                ], 400);
            }

            // OTP verified successfully - find or create user
            $user = User::findOrCreateByPhone($phone, $name);

            // Mark phone as verified
            $user->markPhoneAsVerified();

            // Update login tracking
            $user->updateLoginTracking($request->ip());

            // Transfer session cart to user cart before login
            $sessionId = session()->getId();
            CartItem::transferSessionCartToUser($user->id, $sessionId);

            // Login the user
            Auth::login($user, true); // Remember user
            $request->session()->regenerate();

            // Clear OTP session data
            session()->forget(['otp_phone', 'otp_purpose']);

            // Determine redirect URL
            $redirectUrl = $this->getRedirectUrl($user, $purpose);

            return response()->json([
                'success' => true,
                'message' => 'Login successful!',
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'phone' => $user->formatted_phone,
                    'is_mobile_only' => $user->isMobileOnly()
                ],
                'redirect' => $redirectUrl
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Verification failed. Please try again.'
            ], 500);
        }
    }

    /**
     * Show OTP verification form
     */
    public function showOtpVerification(Request $request)
    {
        $phone = session('otp_phone');
        $purpose = session('otp_purpose', 'login');

        if (!$phone) {
            return redirect()->route('mobile.login')->with('error', 'Please enter your mobile number first.');
        }

        return view('auth.otp-verification', compact('phone', 'purpose'));
    }

    /**
     * Resend OTP
     */
    public function resendOtp(Request $request)
    {
        $phone = session('otp_phone');
        $purpose = session('otp_purpose', 'login');

        if (!$phone) {
            return response()->json([
                'success' => false,
                'message' => 'Session expired. Please start again.'
            ], 400);
        }

        try {
            $result = $this->twilioService->generateAndSendOtp($phone, $purpose, session()->getId(), $request->ip());

            return response()->json([
                'success' => $result['success'],
                'message' => $result['message'],
                'expires_at' => $result['success'] ? $result['expires_at']->format('Y-m-d H:i:s') : null
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to resend OTP. Please try again.'
            ], 500);
        }
    }

    /**
     * Logout user
     */
    public function logout(Request $request)
    {
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return response()->json([
            'success' => true,
            'message' => 'Logged out successfully',
            'redirect' => route('home')
        ]);
    }

    /**
     * Check authentication status
     */
    public function checkAuth(Request $request)
    {
        $user = Auth::user();

        return response()->json([
            'authenticated' => Auth::check(),
            'user' => $user ? [
                'id' => $user->id,
                'name' => $user->name,
                'phone' => $user->formatted_phone,
                'email' => $user->email,
                'is_mobile_only' => $user->isMobileOnly(),
                'can_set_password' => $user->canSetPassword()
            ] : null
        ]);
    }

    /**
     * Set password for mobile-only users (post-purchase option)
     */
    public function setPassword(Request $request)
    {
        $user = Auth::user();

        if (!$user || !$user->canSetPassword()) {
            return response()->json([
                'success' => false,
                'message' => 'You are not eligible to set a password.'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'email' => 'required|email|unique:users,email',
            'password' => 'required|string|min:6|confirmed',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Please check your input.',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user->update([
                'email' => $request->email,
                'password' => bcrypt($request->password),
                'email_verified_at' => now()
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Password set successfully! You can now login with email and password.',
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'phone' => $user->formatted_phone,
                    'is_mobile_only' => false
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to set password. Please try again.'
            ], 500);
        }
    }

    /**
     * Get redirect URL based on user role and purpose
     */
    protected function getRedirectUrl($user, $purpose = 'login')
    {
        // If purpose is checkout, redirect to checkout
        if ($purpose === 'checkout') {
            return route('checkout');
        }

        // Admin users go to admin dashboard
        if ($user->isAdmin()) {
            return route('admin.dashboard');
        }

        // Check if there's an intended URL
        if (Session::has('url.intended')) {
            return Session::get('url.intended');
        }

        // Default to user dashboard
        return route('dashboard');
    }
}
