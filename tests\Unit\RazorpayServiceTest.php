<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\RazorpayService;
use App\Models\User;
use App\Models\Order;
use Razorpay\Api\Api;
use Razorpay\Api\Errors\BadRequestError;
use Razorpay\Api\Errors\SignatureVerificationError;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Mockery;

class RazorpayServiceTest extends TestCase
{
    use RefreshDatabase;

    protected $razorpayService;
    protected $mockApi;
    protected $order;
    protected $user;

    protected function setUp(): void
    {
        parent::setUp();

        // Set test environment variables
        config([
            'services.razorpay.key_id' => 'rzp_test_key',
            'services.razorpay.key_secret' => 'test_secret',
            'services.razorpay.webhook_secret' => 'webhook_secret'
        ]);

        // Create test user
        $this->user = User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>'
        ]);

        // Create test order
        $this->order = Order::create([
            'user_id' => $this->user->id,
            'order_number' => 'TEST_ORDER_123',
            'total_amount' => 100.00,
            'currency' => 'INR',
            'status' => 'pending',
            'payment_status' => 'pending',
            'payment_method' => 'razorpay',
            'shipping_address' => ['name' => 'Test User'],
            'billing_address' => ['name' => 'Test User']
        ]);

        // Mock Razorpay API
        $this->mockApi = Mockery::mock(Api::class);
        
        // Create service instance and inject mock
        $this->razorpayService = new RazorpayService();
        
        // Use reflection to set the protected api property
        $reflection = new \ReflectionClass($this->razorpayService);
        $apiProperty = $reflection->getProperty('api');
        $apiProperty->setAccessible(true);
        $apiProperty->setValue($this->razorpayService, $this->mockApi);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /** @test */
    public function it_can_create_razorpay_order_successfully()
    {
        $mockOrderResponse = [
            'id' => 'order_test_123',
            'amount' => 10000,
            'currency' => 'INR',
            'receipt' => 'TEST_ORDER_123',
            'status' => 'created'
        ];

        $mockOrder = Mockery::mock();
        $mockOrder->shouldReceive('create')
            ->once()
            ->with([
                'receipt' => 'TEST_ORDER_123',
                'amount' => 10000, // 100.00 * 100
                'currency' => 'INR',
                'notes' => [
                    'order_id' => $this->order->id,
                    'customer_name' => 'Test User',
                    'customer_email' => '<EMAIL>',
                ]
            ])
            ->andReturn($mockOrderResponse);

        $this->mockApi->order = $mockOrder;

        $result = $this->razorpayService->createOrder($this->order);

        $this->assertTrue($result['success']);
        $this->assertEquals('order_test_123', $result['order_id']);
        $this->assertEquals(10000, $result['amount']);
        $this->assertEquals('INR', $result['currency']);
        $this->assertEquals('rzp_test_key', $result['key_id']);

        // Verify order was updated with Razorpay order ID
        $this->order->refresh();
        $this->assertEquals('order_test_123', $this->order->payment_gateway_order_id);
    }

    /** @test */
    public function it_handles_razorpay_api_error_during_order_creation()
    {
        $mockOrder = Mockery::mock();
        $mockOrder->shouldReceive('create')
            ->once()
            ->andThrow(new BadRequestError('Invalid request'));

        $this->mockApi->order = $mockOrder;

        $result = $this->razorpayService->createOrder($this->order);

        $this->assertFalse($result['success']);
        $this->assertStringContains('Invalid request', $result['message']);
    }

    /** @test */
    public function it_can_verify_payment_signature_successfully()
    {
        $mockUtility = Mockery::mock();
        $mockUtility->shouldReceive('verifyPaymentSignature')
            ->once()
            ->with([
                'razorpay_order_id' => 'order_test_123',
                'razorpay_payment_id' => 'pay_test_123',
                'razorpay_signature' => 'valid_signature'
            ])
            ->andReturn(true);

        $this->mockApi->utility = $mockUtility;

        $result = $this->razorpayService->verifyPayment(
            'order_test_123',
            'pay_test_123',
            'valid_signature'
        );

        $this->assertTrue($result['success']);
        $this->assertEquals('pay_test_123', $result['payment_id']);
    }

    /** @test */
    public function it_fails_payment_verification_with_invalid_signature()
    {
        $mockUtility = Mockery::mock();
        $mockUtility->shouldReceive('verifyPaymentSignature')
            ->once()
            ->with([
                'razorpay_order_id' => 'order_test_123',
                'razorpay_payment_id' => 'pay_test_123',
                'razorpay_signature' => 'invalid_signature'
            ])
            ->andThrow(new SignatureVerificationError('Invalid signature'));

        $this->mockApi->utility = $mockUtility;

        $result = $this->razorpayService->verifyPayment(
            'order_test_123',
            'pay_test_123',
            'invalid_signature'
        );

        $this->assertFalse($result['success']);
        $this->assertStringContains('Invalid signature', $result['error']);
    }

    /** @test */
    public function it_can_fetch_payment_details_successfully()
    {
        $mockPaymentResponse = [
            'id' => 'pay_test_123',
            'amount' => 10000,
            'currency' => 'INR',
            'status' => 'captured',
            'method' => 'card'
        ];

        $mockPaymentObject = Mockery::mock();
        $mockPaymentObject->shouldReceive('toArray')
            ->once()
            ->andReturn($mockPaymentResponse);

        $mockPayment = Mockery::mock();
        $mockPayment->shouldReceive('fetch')
            ->once()
            ->with('pay_test_123')
            ->andReturn($mockPaymentObject);

        $this->mockApi->payment = $mockPayment;

        $result = $this->razorpayService->getPaymentDetails('pay_test_123');

        $this->assertTrue($result['success']);
        $this->assertEquals($mockPaymentResponse, $result['payment']);
    }

    /** @test */
    public function it_handles_error_when_fetching_payment_details()
    {
        $mockPayment = Mockery::mock();
        $mockPayment->shouldReceive('fetch')
            ->once()
            ->with('invalid_payment_id')
            ->andThrow(new BadRequestError('Payment not found'));

        $this->mockApi->payment = $mockPayment;

        $result = $this->razorpayService->getPaymentDetails('invalid_payment_id');

        $this->assertFalse($result['success']);
        $this->assertEquals('Failed to fetch payment details', $result['error']);
        $this->assertStringContains('Payment not found', $result['message']);
    }

    /** @test */
    public function it_returns_test_card_details()
    {
        $result = $this->razorpayService->getTestCardDetails();

        $this->assertArrayHasKey('success_cards', $result);
        $this->assertArrayHasKey('failure_cards', $result);
        $this->assertArrayHasKey('test_upi', $result);
        $this->assertArrayHasKey('test_netbanking', $result);

        // Verify success cards structure
        $this->assertIsArray($result['success_cards']);
        $this->assertNotEmpty($result['success_cards']);
        
        foreach ($result['success_cards'] as $card) {
            $this->assertArrayHasKey('number', $card);
            $this->assertArrayHasKey('cvv', $card);
            $this->assertArrayHasKey('expiry', $card);
            $this->assertArrayHasKey('name', $card);
        }
    }

    /** @test */
    public function it_processes_webhook_payload_successfully()
    {
        $webhookPayload = [
            'event' => 'payment.captured',
            'payload' => [
                'payment' => [
                    'entity' => [
                        'id' => 'pay_test_123',
                        'order_id' => 'order_test_123',
                        'amount' => 10000,
                        'currency' => 'INR',
                        'status' => 'captured'
                    ]
                ]
            ]
        ];

        $result = $this->razorpayService->processWebhook($webhookPayload, 'valid_signature');

        $this->assertTrue($result['success']);
        $this->assertEquals('payment.captured', $result['event']);
        $this->assertArrayHasKey('payment_data', $result);
    }

    /** @test */
    public function it_validates_webhook_signature()
    {
        $webhookPayload = ['event' => 'payment.captured'];
        
        // Test with invalid signature
        $result = $this->razorpayService->processWebhook($webhookPayload, 'invalid_signature');
        
        $this->assertFalse($result['success']);
        $this->assertStringContains('Invalid webhook signature', $result['error']);
    }

    /** @test */
    public function it_handles_different_currency_conversions()
    {
        // Test with different currency
        $this->order->update(['currency' => 'USD', 'total_amount' => 10.50]);

        $mockOrderResponse = [
            'id' => 'order_test_usd',
            'amount' => 1050, // 10.50 * 100
            'currency' => 'USD',
            'receipt' => 'TEST_ORDER_123',
            'status' => 'created'
        ];

        $mockOrder = Mockery::mock();
        $mockOrder->shouldReceive('create')
            ->once()
            ->with(Mockery::on(function ($args) {
                return $args['amount'] === 1050 && $args['currency'] === 'USD';
            }))
            ->andReturn($mockOrderResponse);

        $this->mockApi->order = $mockOrder;

        $result = $this->razorpayService->createOrder($this->order);

        $this->assertTrue($result['success']);
        $this->assertEquals(1050, $result['amount']);
        $this->assertEquals('USD', $result['currency']);
    }
}
