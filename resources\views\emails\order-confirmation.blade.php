<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Order Confirmation - {{ $order->order_number }}</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .email-container {
            background-color: #ffffff;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 300;
        }
        .content {
            padding: 30px;
        }
        .order-info {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .order-info h2 {
            margin-top: 0;
            color: #495057;
            font-size: 18px;
        }
        .info-row {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .info-row:last-child {
            border-bottom: none;
        }
        .info-label {
            font-weight: 600;
            color: #6c757d;
        }
        .info-value {
            color: #495057;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background-color: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .items-table th {
            background-color: #495057;
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 600;
        }
        .items-table td {
            padding: 15px;
            border-bottom: 1px solid #e9ecef;
        }
        .items-table tr:last-child td {
            border-bottom: none;
        }
        .total-section {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .total-row {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            padding: 5px 0;
        }
        .total-row.final {
            border-top: 2px solid #495057;
            padding-top: 15px;
            margin-top: 15px;
            font-weight: bold;
            font-size: 18px;
            color: #495057;
        }
        .address-section {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        .address-box {
            flex: 1;
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
        }
        .address-box h3 {
            margin-top: 0;
            color: #495057;
            font-size: 16px;
        }
        .footer {
            background-color: #495057;
            color: white;
            padding: 30px;
            text-align: center;
        }
        .footer p {
            margin: 5px 0;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 600;
            margin: 20px 0;
        }
        .success-icon {
            font-size: 48px;
            color: #28a745;
            margin-bottom: 20px;
        }
        @media (max-width: 600px) {
            body {
                padding: 10px;
            }
            .content {
                padding: 20px;
            }
            .address-section {
                flex-direction: column;
            }
            .items-table {
                font-size: 14px;
            }
            .items-table th,
            .items-table td {
                padding: 10px 8px;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="header">
            <div class="success-icon">✓</div>
            <h1>Order Confirmed!</h1>
            <p>Thank you for your purchase, {{ $customer->name }}!</p>
        </div>

        <!-- Content -->
        <div class="content">
            <p>We're excited to let you know that we've received your order and it's being processed. Here are the details:</p>

            <!-- Order Information -->
            <div class="order-info">
                <h2>Order Information</h2>
                <div class="info-row">
                    <span class="info-label">Order Number:</span>
                    <span class="info-value">{{ $order->order_number }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Order Date:</span>
                    <span class="info-value">{{ $order->created_at->format('F j, Y \a\t g:i A') }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Payment Method:</span>
                    <span class="info-value">{{ ucfirst($order->payment_method) }}</span>
                </div>
                <div class="info-row">
                    <span class="info-label">Payment Status:</span>
                    <span class="info-value">{{ ucfirst($order->payment_status) }}</span>
                </div>
            </div>

            <!-- Order Items -->
            <h2>Order Items</h2>
            <table class="items-table">
                <thead>
                    <tr>
                        <th>Product</th>
                        <th>Quantity</th>
                        <th>Price</th>
                        <th>Total</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($orderItems as $item)
                    <tr>
                        <td>
                            <strong>{{ $item->product_name }}</strong>
                            @if($item->size)
                                <br><small>Size: {{ $item->size }}</small>
                            @endif
                            @if($item->product_options)
                                @foreach($item->product_options as $key => $value)
                                    <br><small>{{ ucfirst($key) }}: {{ $value }}</small>
                                @endforeach
                            @endif
                        </td>
                        <td>{{ $item->quantity }}</td>
                        <td>₹{{ number_format($item->price, 2) }}</td>
                        <td>₹{{ number_format($item->total, 2) }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>

            <!-- Order Total -->
            <div class="total-section">
                <div class="total-row">
                    <span>Subtotal:</span>
                    <span>₹{{ number_format($order->subtotal, 2) }}</span>
                </div>
                @if($order->tax_amount > 0)
                <div class="total-row">
                    <span>Tax:</span>
                    <span>₹{{ number_format($order->tax_amount, 2) }}</span>
                </div>
                @endif
                @if($order->shipping_amount > 0)
                <div class="total-row">
                    <span>Shipping:</span>
                    <span>₹{{ number_format($order->shipping_amount, 2) }}</span>
                </div>
                @endif
                @if($order->discount_amount > 0)
                <div class="total-row">
                    <span>Discount:</span>
                    <span>-₹{{ number_format($order->discount_amount, 2) }}</span>
                </div>
                @endif
                <div class="total-row final">
                    <span>Total:</span>
                    <span>₹{{ number_format($order->total_amount, 2) }}</span>
                </div>
            </div>

            <!-- Addresses -->
            <div class="address-section">
                <div class="address-box">
                    <h3>Billing Address</h3>
                    <p>
                        {{ $billingAddress['name'] }}<br>
                        {{ $billingAddress['address'] }}<br>
                        {{ $billingAddress['city'] }}, {{ $billingAddress['state'] }} {{ $billingAddress['pincode'] }}<br>
                        Phone: {{ $billingAddress['phone'] }}<br>
                        Email: {{ $billingAddress['email'] }}
                    </p>
                </div>
                <div class="address-box">
                    <h3>Shipping Address</h3>
                    <p>
                        {{ $shippingAddress['name'] }}<br>
                        {{ $shippingAddress['address'] }}<br>
                        {{ $shippingAddress['city'] }}, {{ $shippingAddress['state'] }} {{ $shippingAddress['pincode'] }}<br>
                        Phone: {{ $shippingAddress['phone'] }}
                    </p>
                </div>
            </div>

            <!-- Call to Action -->
            <div style="text-align: center; margin: 30px 0;">
                <a href="{{ route('order.detail', $order->id) }}" class="btn">Track Your Order</a>
            </div>

            <p>We'll send you another email when your order ships. If you have any questions, please don't hesitate to contact our customer service team.</p>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p><strong>{{ config('app.name') }}</strong></p>
            <p>Thank you for shopping with us!</p>
            <p>Need help? Contact us at {{ config('mail.from.address') }}</p>
        </div>
    </div>
</body>
</html>
