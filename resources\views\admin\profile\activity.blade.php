@extends('layouts.admin')

@section('title', 'Admin Activity Log')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Activity Log</h1>
            <p class="text-muted">Track your admin account activities and login history</p>
        </div>
        <a href="{{ route('admin.profile.show') }}" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>Back to Profile
        </a>
    </div>

    <div class="row">
        <!-- Activity Timeline -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Recent Activities</h5>
                    <div class="d-flex gap-2">
                        <select class="form-select form-select-sm" style="width: auto;">
                            <option>All Activities</option>
                            <option>Login</option>
                            <option>Profile Updates</option>
                            <option>Password Changes</option>
                            <option>Settings</option>
                        </select>
                        <button class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-download me-1"></i>Export
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="activity-timeline">
                        @foreach($activities as $activity)
                        <div class="activity-item">
                            <div class="activity-marker bg-{{ $loop->index % 4 == 0 ? 'primary' : ($loop->index % 4 == 1 ? 'success' : ($loop->index % 4 == 2 ? 'info' : 'warning')) }}"></div>
                            <div class="activity-content">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <div>
                                        <h6 class="mb-1">{{ $activity['action'] }}</h6>
                                        <p class="text-muted mb-1">{{ $activity['description'] }}</p>
                                    </div>
                                    <small class="text-muted">{{ $activity['timestamp']?->diffForHumans() ?? 'Recently' }}</small>
                                </div>
                                <div class="d-flex align-items-center gap-3 small text-muted">
                                    <span><i class="fas fa-clock me-1"></i>{{ $activity['timestamp']?->format('M d, Y g:i A') ?? 'N/A' }}</span>
                                    <span><i class="fas fa-map-marker-alt me-1"></i>{{ $activity['ip_address'] }}</span>
                                    <span class="badge bg-light text-dark">{{ $activity['action'] }}</span>
                                </div>
                            </div>
                        </div>
                        @endforeach

                        <!-- Sample additional activities -->
                        <div class="activity-item">
                            <div class="activity-marker bg-info"></div>
                            <div class="activity-content">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <div>
                                        <h6 class="mb-1">Dashboard Accessed</h6>
                                        <p class="text-muted mb-1">Viewed admin dashboard</p>
                                    </div>
                                    <small class="text-muted">2 hours ago</small>
                                </div>
                                <div class="d-flex align-items-center gap-3 small text-muted">
                                    <span><i class="fas fa-clock me-1"></i>{{ now()->subHours(2)->format('M d, Y g:i A') }}</span>
                                    <span><i class="fas fa-map-marker-alt me-1"></i>{{ request()->ip() }}</span>
                                    <span class="badge bg-light text-dark">Dashboard</span>
                                </div>
                            </div>
                        </div>

                        <div class="activity-item">
                            <div class="activity-marker bg-warning"></div>
                            <div class="activity-content">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <div>
                                        <h6 class="mb-1">Product Created</h6>
                                        <p class="text-muted mb-1">Added new jewelry product to catalog</p>
                                    </div>
                                    <small class="text-muted">5 hours ago</small>
                                </div>
                                <div class="d-flex align-items-center gap-3 small text-muted">
                                    <span><i class="fas fa-clock me-1"></i>{{ now()->subHours(5)->format('M d, Y g:i A') }}</span>
                                    <span><i class="fas fa-map-marker-alt me-1"></i>{{ request()->ip() }}</span>
                                    <span class="badge bg-light text-dark">Product</span>
                                </div>
                            </div>
                        </div>

                        <div class="activity-item">
                            <div class="activity-marker bg-success"></div>
                            <div class="activity-content">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <div>
                                        <h6 class="mb-1">Order Processed</h6>
                                        <p class="text-muted mb-1">Updated order status to shipped</p>
                                    </div>
                                    <small class="text-muted">1 day ago</small>
                                </div>
                                <div class="d-flex align-items-center gap-3 small text-muted">
                                    <span><i class="fas fa-clock me-1"></i>{{ now()->subDay()->format('M d, Y g:i A') }}</span>
                                    <span><i class="fas fa-map-marker-alt me-1"></i>{{ request()->ip() }}</span>
                                    <span class="badge bg-light text-dark">Order</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center mt-4">
                        <nav>
                            <ul class="pagination pagination-sm">
                                <li class="page-item disabled">
                                    <span class="page-link">Previous</span>
                                </li>
                                <li class="page-item active">
                                    <span class="page-link">1</span>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="#">2</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="#">3</a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="#">Next</a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Activity Summary -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Activity Summary</h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-6">
                            <div class="text-center">
                                <div class="h4 text-primary mb-1">24</div>
                                <small class="text-muted">Today</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <div class="h4 text-success mb-1">156</div>
                                <small class="text-muted">This Week</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <div class="h4 text-info mb-1">642</div>
                                <small class="text-muted">This Month</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <div class="h4 text-warning mb-1">2,847</div>
                                <small class="text-muted">Total</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Login Sessions -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Recent Sessions</h5>
                </div>
                <div class="card-body">
                    <div class="session-item mb-3">
                        <div class="d-flex align-items-center">
                            <div class="bg-success rounded-circle me-3" style="width: 10px; height: 10px;"></div>
                            <div class="flex-fill">
                                <h6 class="mb-0">Current Session</h6>
                                <small class="text-muted">{{ request()->ip() }} • {{ request()->userAgent() ? 'Chrome' : 'Unknown' }}</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="session-item mb-3">
                        <div class="d-flex align-items-center">
                            <div class="bg-secondary rounded-circle me-3" style="width: 10px; height: 10px;"></div>
                            <div class="flex-fill">
                                <h6 class="mb-0">Previous Session</h6>
                                <small class="text-muted">{{ request()->ip() }} • 2 hours ago</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="session-item">
                        <div class="d-flex align-items-center">
                            <div class="bg-secondary rounded-circle me-3" style="width: 10px; height: 10px;"></div>
                            <div class="flex-fill">
                                <h6 class="mb-0">Mobile Session</h6>
                                <small class="text-muted">{{ request()->ip() }} • Yesterday</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Security Alerts -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Security Alerts</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-success alert-sm">
                        <i class="fas fa-shield-check me-2"></i>
                        <strong>All Good!</strong> No security issues detected.
                    </div>
                    
                    <div class="small text-muted">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Last Password Change:</span>
                            <span>{{ $user->updated_at?->diffForHumans() ?? 'N/A' }}</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Two-Factor Auth:</span>
                            <span class="text-warning">Disabled</span>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span>Login Alerts:</span>
                            <span class="text-success">Enabled</span>
                        </div>
                    </div>
                    
                    <div class="d-grid mt-3">
                        <button class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-shield-alt me-2"></i>Enable 2FA
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.activity-timeline {
    position: relative;
    padding-left: 30px;
}

.activity-item {
    position: relative;
    margin-bottom: 25px;
}

.activity-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -22px;
    top: 25px;
    width: 2px;
    height: calc(100% + 15px);
    background: #e9ecef;
}

.activity-marker {
    position: absolute;
    left: -26px;
    top: 4px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 0 0 2px #e9ecef;
}

.activity-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid var(--bs-primary);
}

.session-item {
    padding: 10px 0;
    border-bottom: 1px solid #e9ecef;
}

.session-item:last-child {
    border-bottom: none;
}

.alert-sm {
    padding: 8px 12px;
    font-size: 0.875rem;
}
</style>
@endpush
