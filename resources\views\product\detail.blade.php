@extends('layouts.app')

@section('title', ($product->name ?? 'Product') . ' - ShreeJi Jewelry')
@section('description', $product->short_description ?? 'Exquisite jewelry piece from ShreeJi collection.')

@section('content')
    <!-- Breadcrumb -->
    <section class="py-3 bg-light">
        <div class="container">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="{{ route('home') }}">Home</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('collections') }}">Collections</a></li>
                    @if (isset($product) && $product->category)
                        <li class="breadcrumb-item"><a
                                href="{{ route('collections.category', $product->category->slug) }}">{{ $product->category->name }}</a>
                        </li>
                    @endif
                    <li class="breadcrumb-item active" aria-current="page">{{ $product->name ?? 'Product' }}</li>
                </ol>
            </nav>
        </div>
    </section>

    <!-- Product Detail -->
    <section class="py-5">
        <div class="container">
            <div class="row g-5">
                <!-- Product Images -->
                <div class="col-lg-6">
                    <div class="product-gallery">
                        <!-- Main Image -->
                        <div class="main-image mb-3 position-relative">
                            <img src="{{ $product->main_image_url }}" alt="{{ $product->name }}"
                                class="img-fluid rounded-3 shadow-lg" id="mainProductImage">

                            <button class="btn btn-light position-absolute top-50 start-50 translate-middle"
                                data-bs-toggle="modal" data-bs-target="#imageZoomModal">
                                <i class="fas fa-search-plus"></i> Zoom
                            </button>
                        </div>

                        <!-- Thumbnail Images -->
                        @if ($product->images && count($product->images) > 1)
                            <div class="row g-2">
                                @foreach ($product->image_urls as $index => $imageUrl)
                                    @if ($index < 4)
                                        <div class="col-3">
                                            <img src="{{ $imageUrl }}"
                                                alt="{{ $product->name }} View {{ $index + 1 }}"
                                                class="img-fluid rounded-2 thumbnail-img {{ $index === 0 ? 'active' : '' }}"
                                                onclick="changeMainImage('{{ $imageUrl }}')">
                                        </div>
                                    @endif
                                @endforeach
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Product Info -->
                <div class="col-lg-6">
                    <div class="product-info">
                        <div class="d-flex align-items-center gap-2 mb-2">
                            @if ($product->is_featured)
                                <span class="badge bg-primary-pink">Featured</span>
                            @endif
                            @if ($product->in_stock)
                                <span class="badge bg-success">In Stock</span>
                            @else
                                <span class="badge bg-danger">Out of Stock</span>
                            @endif
                            @if ($product->isOnSale())
                                <span class="badge bg-warning">Sale</span>
                            @endif
                        </div>

                        <h1 class="font-playfair display-5 fw-bold mb-3">{{ $product->name }}</h1>

                        <!-- Category -->
                        <div class="mb-3">
                            <span class="text-muted">Category: </span>
                            <a href="{{ route('collections.category', $product->category->slug) }}"
                                class="text-primary-pink text-decoration-none">{{ $product->category->name }}</a>
                        </div>

                        <!-- Price -->
                        <div class="price-section mb-4">
                            <div class="d-flex align-items-center gap-3">
                                @if ($product->isOnSale())
                                    <span
                                        class="h2 text-primary-pink fw-bold mb-0">₹{{ number_format($product->sale_price) }}</span>
                                    <span
                                        class="h5 text-muted text-decoration-line-through mb-0">₹{{ number_format($product->price) }}</span>
                                    <span class="badge bg-danger">{{ $product->discount_percentage }}% OFF</span>
                                @else
                                    <span
                                        class="h2 text-primary-pink fw-bold mb-0">₹{{ number_format($product->price) }}</span>
                                @endif
                            </div>
                            <small class="text-muted">Inclusive of all taxes | Free shipping above ₹25,000</small>
                        </div>

                        <!-- Product Details -->
                        <div class="product-details mb-4">
                            <h5 class="fw-semibold mb-3">Product Details</h5>
                            <ul class="list-unstyled">
                                @if ($product->metal_type)
                                    <li><strong>Metal:</strong> {{ $product->metal_purity }} {{ $product->metal_type }}
                                    </li>
                                @endif
                                @if ($product->stone_type)
                                    <li><strong>Stone:</strong> {{ $product->stone_type }}
                                        @if ($product->stone_weight)
                                            ({{ $product->stone_weight }} Carat)
                                        @endif
                                    </li>
                                @endif
                                @if ($product->weight)
                                    <li><strong>Weight:</strong> {{ $product->weight }} grams</li>
                                @endif
                                @if ($product->certification)
                                    <li><strong>Certification:</strong> {{ $product->certification }}</li>
                                @endif
                                <li><strong>SKU:</strong> {{ $product->sku }}</li>
                            </ul>
                        </div>

                        <!-- Size Selection -->
                        @if($product->sizes && count($product->sizes) > 0)
                        <div class="size-selection mb-4">
                            <h6 class="fw-semibold mb-2">
                                @if(str_contains(strtolower($product->category->name), 'ring'))
                                    Ring Size
                                @else
                                    Size
                                @endif
                            </h6>
                            <div class="d-flex gap-2 flex-wrap">
                                @foreach($product->sizes as $index => $size)
                                    <input type="radio" class="btn-check" name="size" id="size-{{ $size }}" value="{{ $size }}" {{ $index === 0 ? 'checked' : '' }}>
                                    <label class="btn btn-outline-secondary" for="size-{{ $size }}">{{ $size }}</label>
                                @endforeach
                            </div>
                            <small class="text-muted">
                                @if(str_contains(strtolower($product->category->name), 'ring'))
                                    <a href="#" class="text-primary-pink">Size Guide</a> |
                                @endif
                                <a href="#" class="text-primary-pink">Free Resizing</a>
                            </small>
                        </div>
                        @endif

                        <!-- Quantity -->
                        <div class="quantity-section mb-4">
                            <h6 class="fw-semibold mb-2">Quantity</h6>
                            <div class="input-group" style="width: 150px;">
                                <button class="btn btn-outline-secondary" type="button"
                                    onclick="decreaseQuantity()">-</button>
                                <input type="number" class="form-control text-center" value="1" min="1"
                                    id="quantity">
                                <button class="btn btn-outline-secondary" type="button"
                                    onclick="increaseQuantity()">+</button>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="action-buttons mb-4">
                            @if ($product->in_stock)
                                <div class="d-flex gap-3 flex-wrap">
                                    <button class="btn btn-primary-pink btn-lg flex-fill" id="addToCartBtn"
                                        data-product-id="{{ $product->id }}">
                                        <i class="fas fa-shopping-cart me-2"></i>Add to Cart
                                    </button>
                                    <button class="btn btn-outline-pink btn-lg" id="wishlistBtn"
                                        data-product-id="{{ $product->id }}">
                                        <i class="fas fa-heart me-2"></i>Wishlist
                                    </button>
                                </div>
                                <button class="btn btn-success btn-lg w-100 mt-3" id="buyNowBtn"
                                    data-product-id="{{ $product->id }}">
                                    <i class="fas fa-bolt me-2"></i>Buy Now
                                </button>
                            @else
                                <div class="d-grid gap-2">
                                    <button class="btn btn-secondary btn-lg" disabled>
                                        <i class="fas fa-times me-2"></i>Out of Stock
                                    </button>
                                    <button class="btn btn-outline-primary" id="notifyBtn"
                                        data-product-id="{{ $product->id }}">
                                        <i class="fas fa-bell me-2"></i>Notify When Available
                                    </button>
                                </div>
                            @endif
                        </div>

                        <!-- Features -->
                        <div class="features">
                            <div class="row g-3">
                                <div class="col-6">
                                    <div class="d-flex align-items-center gap-2">
                                        <i class="fas fa-shipping-fast text-primary-pink"></i>
                                        <small>Free Shipping</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="d-flex align-items-center gap-2">
                                        <i class="fas fa-undo text-primary-pink"></i>
                                        <small>30-Day Returns</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="d-flex align-items-center gap-2">
                                        <i class="fas fa-certificate text-primary-pink"></i>
                                        <small>Lifetime Warranty</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="d-flex align-items-center gap-2">
                                        <i class="fas fa-shield-alt text-primary-pink"></i>
                                        <small>Secure Payment</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Product Tabs -->
    <section class="py-5 bg-light">
        <div class="container">
            <ul class="nav nav-tabs justify-content-center mb-4" id="productTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="description-tab" data-bs-toggle="tab"
                        data-bs-target="#description" type="button" role="tab">
                        Description
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="specifications-tab" data-bs-toggle="tab"
                        data-bs-target="#specifications" type="button" role="tab">
                        Specifications
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="reviews-tab" data-bs-toggle="tab" data-bs-target="#reviews"
                        type="button" role="tab">
                        Reviews ({{ $product->review_count }})
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="care-tab" data-bs-toggle="tab" data-bs-target="#care" type="button"
                        role="tab">
                        Care Instructions
                    </button>
                </li>
            </ul>

            <div class="tab-content" id="productTabsContent">
                <!-- Description Tab -->
                <div class="tab-pane fade show active" id="description" role="tabpanel">
                    <div class="row justify-content-center">
                        <div class="col-lg-8">
                            <div class="card border-0 shadow-sm">
                                <div class="card-body p-4">
                                    <h5 class="font-playfair mb-3">About This {{ $product->name }}</h5>

                                    @if($product->short_description)
                                        <div class="alert alert-light border-start border-primary-pink border-4 mb-4">
                                            <p class="mb-0 fw-semibold">{{ $product->short_description }}</p>
                                        </div>
                                    @endif

                                    <div class="description-content">
                                        {!! nl2br(e($product->description)) !!}
                                    </div>

                                    @if($product->certification)
                                        <div class="mt-4 p-3 bg-light rounded">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-certificate text-primary-pink me-2"></i>
                                                <strong>Certification:</strong>
                                                <span class="ms-2">{{ $product->certification }}</span>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Specifications Tab -->
                <div class="tab-pane fade" id="specifications" role="tabpanel">
                    <div class="row justify-content-center">
                        <div class="col-lg-8">
                            <div class="card border-0 shadow-sm">
                                <div class="card-body p-4">
                                    <h5 class="font-playfair mb-3">Technical Specifications</h5>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <table class="table table-borderless">
                                                @if($product->metal_type)
                                                <tr>
                                                    <td><strong>Metal Type:</strong></td>
                                                    <td>{{ $product->metal_purity }} {{ $product->metal_type }}</td>
                                                </tr>
                                                @endif
                                                @if($product->weight)
                                                <tr>
                                                    <td><strong>Total Weight:</strong></td>
                                                    <td>{{ $product->weight }} grams</td>
                                                </tr>
                                                @endif
                                                @if($product->sku)
                                                <tr>
                                                    <td><strong>SKU:</strong></td>
                                                    <td>{{ $product->sku }}</td>
                                                </tr>
                                                @endif
                                                @if($product->sizes && count($product->sizes) > 0)
                                                <tr>
                                                    <td><strong>Available Sizes:</strong></td>
                                                    <td>{{ implode(', ', $product->sizes) }}</td>
                                                </tr>
                                                @endif
                                                @if($product->certification)
                                                <tr>
                                                    <td><strong>Certification:</strong></td>
                                                    <td>{{ $product->certification }}</td>
                                                </tr>
                                                @endif
                                            </table>
                                        </div>
                                        <div class="col-md-6">
                                            <table class="table table-borderless">
                                                @if($product->stone_type)
                                                <tr>
                                                    <td><strong>Stone Type:</strong></td>
                                                    <td>{{ $product->stone_type }}</td>
                                                </tr>
                                                @endif
                                                @if($product->stone_weight)
                                                <tr>
                                                    <td><strong>Stone Weight:</strong></td>
                                                    <td>{{ $product->stone_weight }} Carat</td>
                                                </tr>
                                                @endif
                                                @if($product->specifications && is_array($product->specifications))
                                                    @foreach($product->specifications as $key => $value)
                                                        @if($value)
                                                        <tr>
                                                            <td><strong>{{ ucwords(str_replace('_', ' ', $key)) }}:</strong></td>
                                                            <td>{{ $value }}</td>
                                                        </tr>
                                                        @endif
                                                    @endforeach
                                                @endif
                                                <tr>
                                                    <td><strong>Stock Status:</strong></td>
                                                    <td>
                                                        @if($product->in_stock)
                                                            <span class="badge bg-success">In Stock</span>
                                                            @if($product->manage_stock)
                                                                <small class="text-muted">({{ $product->stock_quantity }} available)</small>
                                                            @endif
                                                        @else
                                                            <span class="badge bg-danger">Out of Stock</span>
                                                        @endif
                                                    </td>
                                                </tr>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Reviews Tab -->
                <div class="tab-pane fade" id="reviews" role="tabpanel">
                    <div class="row justify-content-center">
                        <div class="col-lg-8">
                            @if($product->review_count > 0)
                                <!-- Review Summary -->
                                <div class="card border-0 shadow-sm mb-4">
                                    <div class="card-body p-4">
                                        <div class="row align-items-center">
                                            <div class="col-md-4 text-center">
                                                <h2 class="display-4 text-primary-pink mb-0">{{ number_format($product->average_rating, 1) }}</h2>
                                                <div class="stars mb-2">
                                                    @for($i = 1; $i <= 5; $i++)
                                                        @if($i <= floor($product->average_rating))
                                                            <i class="fas fa-star text-warning"></i>
                                                        @elseif($i - 0.5 <= $product->average_rating)
                                                            <i class="fas fa-star-half-alt text-warning"></i>
                                                        @else
                                                            <i class="far fa-star text-warning"></i>
                                                        @endif
                                                    @endfor
                                                </div>
                                                <p class="text-muted mb-0">Based on {{ $product->review_count }} {{ Str::plural('review', $product->review_count) }}</p>
                                            </div>
                                            <div class="col-md-8">
                                                <div class="rating-breakdown">
                                                    @php $breakdown = $product->rating_breakdown; @endphp
                                                    @for($rating = 5; $rating >= 1; $rating--)
                                                        @php
                                                            $count = $breakdown[$rating] ?? 0;
                                                            $percentage = $product->review_count > 0 ? ($count / $product->review_count) * 100 : 0;
                                                        @endphp
                                                        <div class="d-flex align-items-center mb-2">
                                                            <span class="me-2">{{ $rating }}★</span>
                                                            <div class="progress flex-fill me-2">
                                                                <div class="progress-bar bg-warning" style="width: {{ $percentage }}%"></div>
                                                            </div>
                                                            <span class="text-muted">{{ $count }}</span>
                                                        </div>
                                                    @endfor
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endif

                            <!-- Individual Reviews -->
                            @if($reviews->count() > 0)
                                <div class="card border-0 shadow-sm">
                                    <div class="card-body p-4">
                                        @foreach($reviews as $review)
                                            <div class="review-item {{ !$loop->last ? 'border-bottom pb-4 mb-4' : '' }}">
                                                <div class="d-flex align-items-start gap-3">
                                                    @if($review->user->avatar)
                                                        <img src="{{ asset('storage/' . $review->user->avatar) }}"
                                                             alt="{{ $review->user->name }}" class="rounded-circle" width="50" height="50">
                                                    @else
                                                        <div class="rounded-circle bg-primary-pink d-flex align-items-center justify-content-center text-white"
                                                             style="width: 50px; height: 50px; font-weight: bold;">
                                                            {{ strtoupper(substr($review->user->name, 0, 1)) }}
                                                        </div>
                                                    @endif
                                                    <div class="flex-fill">
                                                        <div class="d-flex justify-content-between align-items-start mb-2">
                                                            <div>
                                                                <h6 class="mb-0">{{ $review->user->name }}</h6>
                                                                <small class="text-muted">
                                                                    @if($review->is_verified_purchase)
                                                                        <i class="fas fa-check-circle text-success me-1"></i>Verified Purchase •
                                                                    @endif
                                                                    {{ $review->created_at->diffForHumans() }}
                                                                </small>
                                                            </div>
                                                            <div class="stars">
                                                                @for($i = 1; $i <= 5; $i++)
                                                                    @if($i <= $review->rating)
                                                                        <i class="fas fa-star text-warning"></i>
                                                                    @else
                                                                        <i class="far fa-star text-warning"></i>
                                                                    @endif
                                                                @endfor
                                                            </div>
                                                        </div>

                                                        @if($review->title)
                                                            <h6 class="mb-2 fw-semibold">{{ $review->title }}</h6>
                                                        @endif

                                                        <p class="mb-2">{{ $review->comment }}</p>

                                                        <small class="text-muted">
                                                            Helpful?
                                                            <a href="#" class="text-primary-pink helpful-btn" data-review-id="{{ $review->id }}">
                                                                Yes ({{ $review->helpful_count }})
                                                            </a> |
                                                            <a href="#" class="text-primary-pink">Report</a>
                                                        </small>
                                                    </div>
                                                </div>
                                            </div>
                                        @endforeach

                                        @if($reviews->hasPages())
                                            <div class="d-flex justify-content-center mt-4">
                                                {{ $reviews->links() }}
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            @else
                                <div class="card border-0 shadow-sm">
                                    <div class="card-body p-4 text-center">
                                        <i class="fas fa-star-o fa-3x text-muted mb-3"></i>
                                        <h5 class="text-muted">No Reviews Yet</h5>
                                        <p class="text-muted">Be the first to review this product!</p>
                                        @auth
                                            <button class="btn btn-primary-pink" data-bs-toggle="modal" data-bs-target="#reviewModal">
                                                <i class="fas fa-star me-2"></i>Write a Review
                                            </button>
                                        @else
                                            <a href="{{ route('login') }}" class="btn btn-primary-pink">
                                                <i class="fas fa-sign-in-alt me-2"></i>Login to Write a Review
                                            </a>
                                        @endauth
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Care Instructions Tab -->
                <div class="tab-pane fade" id="care" role="tabpanel">
                    <div class="row justify-content-center">
                        <div class="col-lg-8">
                            <div class="card border-0 shadow-sm">
                                <div class="card-body p-4">
                                    <h5 class="font-playfair mb-3">Jewelry Care Instructions</h5>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <h6 class="text-primary-pink">Daily Care</h6>
                                            <ul class="list-unstyled">
                                                <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Remove
                                                    before swimming or bathing</li>
                                                <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Store in
                                                    a soft pouch when not wearing</li>
                                                <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Avoid
                                                    contact with perfumes and lotions</li>
                                                <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Remove
                                                    during household cleaning</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-6">
                                            <h6 class="text-primary-pink">Cleaning</h6>
                                            <ul class="list-unstyled">
                                                <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Clean
                                                    with warm soapy water</li>
                                                <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Use a
                                                    soft toothbrush for gentle scrubbing</li>
                                                <li class="mb-2"><i
                                                        class="fas fa-check text-success me-2"></i>Professional cleaning
                                                    every 6 months</li>
                                                <li class="mb-2"><i class="fas fa-check text-success me-2"></i>Dry with
                                                    a soft, lint-free cloth</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Related Products -->
    @if ($relatedProducts && $relatedProducts->count() > 0)
        <section class="py-5 bg-light">
            <div class="container">
                <h2 class="section-title text-center mb-5">You May Also Like</h2>

                <div class="row g-4">
                    @foreach ($relatedProducts as $relatedProduct)
                        <div class="col-lg-3 col-md-6">
                            <div class="card product-card h-100">
                                <div class="position-relative overflow-hidden product-image-container">
                                    <img src="{{ $relatedProduct->main_image_url }}" class="card-img-top"
                                        alt="{{ $relatedProduct->name }}">

                                    <div class="product-overlay">
                                        <div class="d-flex flex-column gap-2">
                                            <div class="d-flex gap-2 justify-content-center">
                                                <a href="{{ route('product.detail', $relatedProduct->slug) }}"
                                                    class="btn btn-light rounded-pill btn-sm">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <button class="btn btn-primary-pink rounded-pill btn-sm add-to-cart-btn"
                                                    data-product-id="{{ $relatedProduct->id }}">
                                                    <i class="fas fa-shopping-cart"></i>
                                                </button>
                                                <button class="btn btn-light rounded-pill btn-sm wishlist-btn"
                                                    data-product-id="{{ $relatedProduct->id }}">
                                                    <i class="fas fa-heart"></i>
                                                </button>
                                            </div>
                                            <div class="d-flex justify-content-center">
                                                <button class="btn btn-success rounded-pill btn-sm buy-now-btn"
                                                    data-product-id="{{ $relatedProduct->id }}">
                                                    <i class="fas fa-bolt me-1"></i>Buy Now
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    @if ($relatedProduct->isOnSale())
                                        <span class="badge bg-danger position-absolute top-0 start-0 m-2">
                                            {{ $relatedProduct->discount_percentage }}% OFF
                                        </span>
                                    @elseif($relatedProduct->is_featured)
                                        <span
                                            class="badge bg-primary-pink position-absolute top-0 start-0 m-2">Featured</span>
                                    @endif
                                </div>
                                <div class="card-body text-center d-flex flex-column">
                                    <h5 class="card-title font-playfair">
                                        <a href="{{ route('product.detail', $relatedProduct->slug) }}"
                                            class="text-decoration-none text-dark">
                                            {{ $relatedProduct->name }}
                                        </a>
                                    </h5>
                                    <p class="card-text text-muted small">{{ $relatedProduct->category->name }}</p>
                                    <div class="d-flex justify-content-center align-items-center gap-2 mb-2">
                                        @if ($relatedProduct->isOnSale())
                                            <span
                                                class="text-primary-pink fw-bold fs-5">₹{{ number_format($relatedProduct->sale_price) }}</span>
                                            <span
                                                class="text-muted text-decoration-line-through small">₹{{ number_format($relatedProduct->price) }}</span>
                                        @else
                                            <span
                                                class="text-primary-pink fw-bold fs-5">₹{{ number_format($relatedProduct->price) }}</span>
                                        @endif
                                    </div>
                                    @if ($relatedProduct->metal_type || $relatedProduct->stone_type)
                                        <div class="mt-auto">
                                            @if ($relatedProduct->metal_type)
                                                <small
                                                    class="badge bg-light text-dark me-1">{{ $relatedProduct->metal_type }}</small>
                                            @endif
                                            @if ($relatedProduct->stone_type)
                                                <small
                                                    class="badge bg-light text-dark">{{ $relatedProduct->stone_type }}</small>
                                            @endif
                                        </div>
                                    @endif

                                    <!-- Action Buttons for Mobile -->
                                    <div class="d-flex gap-2 mt-3 d-md-none">
                                        <button class="btn btn-primary-pink btn-sm flex-fill add-to-cart-btn-mobile"
                                            data-product-id="{{ $relatedProduct->id }}">
                                            <i class="fas fa-shopping-cart me-1"></i>Add to Cart
                                        </button>
                                        <button class="btn btn-success btn-sm flex-fill buy-now-btn-mobile"
                                            data-product-id="{{ $relatedProduct->id }}">
                                            <i class="fas fa-bolt me-1"></i>Buy Now
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- View More Related Products -->
                <div class="text-center mt-4">
                    <a href="{{ route('collections.category', $product->category->slug) }}" class="btn btn-outline-pink">
                        <i class="fas fa-eye me-2"></i>View More {{ $product->category->name }}
                    </a>
                </div>
            </div>
        </section>
    @endif

    <!-- Image Zoom Modal -->
    <div class="modal fade" id="imageZoomModal" tabindex="-1">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header border-0">
                    <h5 class="modal-title">{{ $product->name }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body text-center">
                    <img src="{{ $product->main_image_url }}" alt="{{ $product->name }}" class="img-fluid" id="zoomModalImage">
                </div>
            </div>
        </div>
    </div>
@endsection

@push('styles')
    <style>
        .thumbnail-img {
            cursor: pointer;
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .thumbnail-img:hover,
        .thumbnail-img.active {
            border-color: var(--primary-pink);
        }

        .main-image {
            position: relative;
        }

        .main-image button {
            opacity: 0;
            transition: all 0.3s ease;
        }

        .main-image:hover button {
            opacity: 1;
        }

        .nav-tabs .nav-link {
            color: var(--text-dark);
            border: none;
            border-bottom: 2px solid transparent;
            background: none;
        }

        .nav-tabs .nav-link.active {
            color: var(--primary-pink);
            border-bottom-color: var(--primary-pink);
            background: none;
        }

        .btn-check:checked+.btn-outline-secondary {
            background-color: var(--primary-pink);
            border-color: var(--primary-pink);
            color: white;
        }

        .progress-bar {
            background-color: var(--primary-brown) !important;
        }

        /* Related Products Section */
        .section-title {
            position: relative;
            font-family: 'Playfair Display', serif;
            color: var(--text-dark);
            margin-bottom: 2rem;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 3px;
            background: linear-gradient(45deg, var(--primary-brown), var(--secondary-brown));
            border-radius: 2px;
        }

        /* Product Cards in Related Section */
        .product-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: none;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .product-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15) !important;
        }

        .product-image-container {
            height: 250px;
            overflow: hidden;
        }

        .product-card .card-img-top {
            height: 100%;
            width: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .product-card:hover .card-img-top {
            transform: scale(1.05);
        }

        .product-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.6);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 2;
        }

        .product-card:hover .product-overlay {
            opacity: 1;
        }

        .product-overlay .btn {
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .product-overlay .btn:hover {
            transform: scale(1.1);
            transition: transform 0.2s ease;
        }

        /* Mobile action buttons */
        @media (max-width: 767.98px) {
            .product-overlay {
                display: none;
            }
        }

        @media (min-width: 768px) {

            .add-to-cart-btn-mobile,
            .buy-now-btn-mobile {
                display: none !important;
            }
        }
    </style>
@endpush

@push('scripts')
    <script>
        function changeMainImage(src) {
            document.getElementById('mainProductImage').src = src;

            // Update zoom modal image
            const zoomModalImage = document.getElementById('zoomModalImage');
            if (zoomModalImage) {
                zoomModalImage.src = src;
            }

            // Update active thumbnail
            document.querySelectorAll('.thumbnail-img').forEach(img => {
                img.classList.remove('active');
            });
            event.target.classList.add('active');
        }

        function increaseQuantity() {
            const input = document.getElementById('quantity');
            const max = parseInt(input.getAttribute('max')) || 999;
            if (parseInt(input.value) < max) {
                input.value = parseInt(input.value) + 1;
            }
        }

        function decreaseQuantity() {
            const input = document.getElementById('quantity');
            if (parseInt(input.value) > 1) {
                input.value = parseInt(input.value) - 1;
            }
        }

        // Add to Cart functionality
        document.addEventListener('DOMContentLoaded', function() {
            const addToCartBtn = document.getElementById('addToCartBtn');
            const buyNowBtn = document.getElementById('buyNowBtn');
            const wishlistBtn = document.getElementById('wishlistBtn');

            if (addToCartBtn) {
                addToCartBtn.addEventListener('click', function() {
                    const productId = this.dataset.productId;
                    const quantity = document.getElementById('quantity').value;
                    const size = document.querySelector('input[name="size"]:checked')?.value;

                    // Check if size is required and selected
                    const sizeInputs = document.querySelectorAll('input[name="size"]');
                    if (sizeInputs.length > 0 && !size) {
                        showNotification('Please select a size', 'error');
                        return;
                    }

                    // Add to cart
                    fetch('/cart/add', {
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')
                                    .getAttribute('content'),
                                'Content-Type': 'application/json',
                                'Accept': 'application/json',
                            },
                            body: JSON.stringify({
                                product_id: productId,
                                quantity: parseInt(quantity),
                                size: size
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                showNotification('Product added to cart!', 'success');
                                updateCartCount(data.cart_count);
                            } else {
                                showNotification(data.message || 'Error adding product to cart',
                                    'error');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            showNotification('Error adding product to cart', 'error');
                        });
                });
            }

            // Buy Now functionality
            if (buyNowBtn) {
                buyNowBtn.addEventListener('click', function() {
                    const productId = this.dataset.productId;
                    const quantity = document.getElementById('quantity').value;
                    const size = document.querySelector('input[name="size"]:checked')?.value;

                    // Check if size is required and selected
                    const sizeInputs = document.querySelectorAll('input[name="size"]');
                    if (sizeInputs.length > 0 && !size) {
                        showNotification('Please select a size', 'error');
                        return;
                    }

                    // Add to cart and redirect to checkout
                    fetch('/cart/add', {
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')
                                    .getAttribute('content'),
                                'Content-Type': 'application/json',
                                'Accept': 'application/json',
                            },
                            body: JSON.stringify({
                                product_id: productId,
                                quantity: parseInt(quantity),
                                size: size,
                                buy_now: true
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // Redirect to checkout
                                window.location.href = '/checkout';
                            } else {
                                showNotification(data.message || 'Error processing request', 'error');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            showNotification('Error processing request', 'error');
                        });
                });
            }

            // Wishlist functionality
            if (wishlistBtn) {
                wishlistBtn.addEventListener('click', function() {
                    const productId = this.dataset.productId;

                    fetch('/wishlist/toggle', {
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')
                                    .getAttribute('content'),
                                'Content-Type': 'application/json',
                                'Accept': 'application/json',
                            },
                            body: JSON.stringify({
                                product_id: productId
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                const icon = this.querySelector('i');
                                if (data.is_added) {
                                    icon.classList.remove('far');
                                    icon.classList.add('fas');
                                    this.classList.remove('btn-outline-pink');
                                    this.classList.add('btn-danger');
                                } else {
                                    icon.classList.remove('fas');
                                    icon.classList.add('far');
                                    this.classList.remove('btn-danger');
                                    this.classList.add('btn-outline-pink');
                                }
                                showNotification(data.message, 'success');
                            } else {
                                showNotification(data.message || 'Error updating wishlist', 'error');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            showNotification('Please login to add items to wishlist', 'error');
                        });
                });
            }
        });

        // Utility functions
        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.className =
                `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 3000);
        }

        function updateCartCount(count) {
            const cartCountElements = document.querySelectorAll('.cart-count');
            cartCountElements.forEach(element => {
                element.textContent = count;
            });
        }

        // Related Products functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Add to cart functionality for related products
            document.querySelectorAll('.add-to-cart-btn, .add-to-cart-btn-mobile').forEach(button => {
                button.addEventListener('click', function() {
                    const productId = this.dataset.productId;

                    fetch('/cart/add', {
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector(
                                    'meta[name="csrf-token"]').getAttribute('content'),
                                'Content-Type': 'application/json',
                                'Accept': 'application/json',
                            },
                            body: JSON.stringify({
                                product_id: productId,
                                quantity: 1
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                showNotification('Product added to cart!', 'success');
                                if (data.cart_count) {
                                    updateCartCount(data.cart_count);
                                }
                            } else {
                                showNotification(data.message || 'Error adding product to cart',
                                    'error');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            showNotification('Error adding product to cart', 'error');
                        });
                });
            });

            // Buy Now functionality for related products
            document.querySelectorAll('.buy-now-btn, .buy-now-btn-mobile').forEach(button => {
                button.addEventListener('click', function() {
                    const productId = this.dataset.productId;

                    // Add loading state
                    const originalText = this.innerHTML;
                    this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Processing...';
                    this.disabled = true;

                    fetch('/cart/add', {
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector(
                                    'meta[name="csrf-token"]').getAttribute('content'),
                                'Content-Type': 'application/json',
                                'Accept': 'application/json',
                            },
                            body: JSON.stringify({
                                product_id: productId,
                                quantity: 1,
                                buy_now: true
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // Redirect to checkout
                                window.location.href = '/checkout';
                            } else {
                                // Restore button state
                                this.innerHTML = originalText;
                                this.disabled = false;
                                showNotification(data.message || 'Error processing request',
                                    'error');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            // Restore button state
                            this.innerHTML = originalText;
                            this.disabled = false;
                            showNotification('Error processing request', 'error');
                        });
                });
            });

            // Wishlist functionality for related products
            document.querySelectorAll('.wishlist-btn').forEach(button => {
                button.addEventListener('click', function() {
                    const productId = this.dataset.productId;

                    fetch('/wishlist/toggle', {
                            method: 'POST',
                            headers: {
                                'X-CSRF-TOKEN': document.querySelector(
                                    'meta[name="csrf-token"]').getAttribute('content'),
                                'Content-Type': 'application/json',
                                'Accept': 'application/json',
                            },
                            body: JSON.stringify({
                                product_id: productId
                            })
                        })
                        .then(response => response.json())
                        .then(data => {
                            if (data.success) {
                                // Update heart icon
                                const icon = this.querySelector('i');
                                if (data.is_added) {
                                    icon.classList.remove('far');
                                    icon.classList.add('fas');
                                    this.classList.remove('btn-light');
                                    this.classList.add('btn-danger');
                                } else {
                                    icon.classList.remove('fas');
                                    icon.classList.add('far');
                                    this.classList.remove('btn-danger');
                                    this.classList.add('btn-light');
                                }

                                showNotification(data.message, 'success');
                            } else {
                                showNotification(data.message || 'Error updating wishlist',
                                    'error');
                            }
                        })
                        .catch(error => {
                            console.error('Error:', error);
                            showNotification('Please login to add items to wishlist', 'error');
                        });
                });
            });
        });
    </script>
@endpush
