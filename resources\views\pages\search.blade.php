@extends('layouts.app')

@section('title', 'Search Results - ShreeJi Jewelry')
@section('description', 'Search results for jewelry products at ShreeJi.')

@section('content')
<!-- Search Header -->
<section class="py-4 bg-light">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="font-playfair display-6 mb-2">Search Results</h1>
                @if($searchTerm)
                    <p class="text-muted mb-0">
                        @if($products->count() > 0)
                            Found {{ $products->total() }} results for "{{ $searchTerm }}"
                        @else
                            No results found for "{{ $searchTerm }}"
                        @endif
                    </p>
                @else
                    <p class="text-muted mb-0">Enter a search term to find products</p>
                @endif
            </div>
            <div class="col-md-4">
                <form method="GET" action="{{ route('search') }}" class="d-flex">
                    <input type="text" name="q" class="form-control me-2" 
                           placeholder="Search jewelry..." value="{{ $searchTerm }}">
                    <button type="submit" class="btn btn-primary-pink">
                        <i class="fas fa-search"></i>
                    </button>
                </form>
            </div>
        </div>
    </div>
</section>

@if($searchTerm && $products->count() > 0)
<!-- Search Results -->
<section class="py-5">
    <div class="container">
        <div class="row g-4">
            @foreach($products as $product)
            <div class="col-lg-3 col-md-4 col-sm-6">
                <div class="card product-card border-0 shadow-sm h-100">
                    <div class="position-relative overflow-hidden">
                        @if($product->images && count($product->images) > 0)
                            <img src="{{ $product->images[0] }}" alt="{{ $product->name }}" 
                                 class="card-img-top product-image">
                        @else
                            <img src="https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80" 
                                 alt="{{ $product->name }}" class="card-img-top product-image">
                        @endif
                        
                        @if($product->isOnSale())
                            <span class="badge bg-danger position-absolute top-0 start-0 m-2">
                                {{ $product->discount_percentage }}% OFF
                            </span>
                        @endif
                        
                        @if($product->is_featured)
                            <span class="badge bg-primary-pink position-absolute top-0 end-0 m-2">
                                Featured
                            </span>
                        @endif
                        
                        <div class="product-overlay">
                            <div class="d-flex gap-2">
                                <button class="btn btn-light btn-sm rounded-circle wishlist-btn" 
                                        data-product-id="{{ $product->id }}">
                                    <i class="far fa-heart"></i>
                                </button>
                                <a href="{{ route('product.detail', $product->slug) }}" 
                                   class="btn btn-light btn-sm rounded-circle">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card-body p-3">
                        <div class="mb-2">
                            <small class="text-muted">{{ $product->category->name }}</small>
                        </div>
                        <h6 class="card-title mb-2">
                            <a href="{{ route('product.detail', $product->slug) }}" 
                               class="text-decoration-none text-dark">
                                {{ $product->name }}
                            </a>
                        </h6>
                        <p class="card-text text-muted small mb-3">{{ Str::limit($product->short_description, 60) }}</p>
                        
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="price">
                                @if($product->isOnSale())
                                    <span class="text-primary-pink fw-bold">₹{{ number_format($product->sale_price) }}</span>
                                    <small class="text-muted text-decoration-line-through ms-1">₹{{ number_format($product->price) }}</small>
                                @else
                                    <span class="text-primary-pink fw-bold">₹{{ number_format($product->price) }}</span>
                                @endif
                            </div>
                            <button class="btn btn-primary-pink btn-sm add-to-cart-btn" 
                                    data-product-id="{{ $product->id }}">
                                <i class="fas fa-shopping-cart me-1"></i>Add
                            </button>
                        </div>
                        
                        @if($product->metal_type || $product->stone_type)
                        <div class="mt-2">
                            @if($product->metal_type)
                                <small class="badge bg-light text-dark me-1">{{ $product->metal_type }}</small>
                            @endif
                            @if($product->stone_type)
                                <small class="badge bg-light text-dark">{{ $product->stone_type }}</small>
                            @endif
                        </div>
                        @endif
                    </div>
                </div>
            </div>
            @endforeach
        </div>
        
        <!-- Pagination -->
        @if($products->hasPages())
        <div class="row mt-5">
            <div class="col-12">
                <nav aria-label="Search results pagination">
                    {{ $products->appends(request()->query())->links() }}
                </nav>
            </div>
        </div>
        @endif
    </div>
</section>

@elseif($searchTerm)
<!-- No Results -->
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-6 text-center">
                <i class="fas fa-search text-muted mb-4" style="font-size: 4rem;"></i>
                <h3 class="font-playfair mb-3">No Results Found</h3>
                <p class="text-muted mb-4">
                    We couldn't find any products matching "{{ $searchTerm }}". 
                    Try searching with different keywords or browse our categories.
                </p>
                <div class="d-flex gap-3 justify-content-center">
                    <a href="{{ route('collections') }}" class="btn btn-primary-pink">
                        Browse All Products
                    </a>
                    <a href="{{ route('home') }}" class="btn btn-outline-secondary">
                        Back to Home
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

@else
<!-- Search Suggestions -->
<section class="py-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-8 text-center">
                <i class="fas fa-search text-primary-pink mb-4" style="font-size: 4rem;"></i>
                <h3 class="font-playfair mb-3">Search Our Jewelry Collection</h3>
                <p class="text-muted mb-4">
                    Find the perfect piece from our extensive collection of rings, necklaces, earrings, and more.
                </p>
                
                <div class="row g-3 mb-5">
                    <div class="col-md-3 col-6">
                        <a href="{{ route('collections', ['search' => 'diamond']) }}" 
                           class="btn btn-outline-primary w-100">Diamond</a>
                    </div>
                    <div class="col-md-3 col-6">
                        <a href="{{ route('collections', ['search' => 'gold']) }}" 
                           class="btn btn-outline-primary w-100">Gold</a>
                    </div>
                    <div class="col-md-3 col-6">
                        <a href="{{ route('collections', ['search' => 'silver']) }}" 
                           class="btn btn-outline-primary w-100">Silver</a>
                    </div>
                    <div class="col-md-3 col-6">
                        <a href="{{ route('collections', ['search' => 'pearl']) }}" 
                           class="btn btn-outline-primary w-100">Pearl</a>
                    </div>
                </div>
                
                <h5 class="font-playfair mb-3">Browse by Category</h5>
                <div class="row g-3">
                    @foreach($globalCategories as $category)
                    <div class="col-md-2 col-4">
                        <a href="{{ route('collections.category', $category->slug) }}" 
                           class="btn btn-light w-100">{{ $category->name }}</a>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</section>
@endif
@endsection

@push('styles')
<style>
    .product-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    
    .product-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.1) !important;
    }
    
    .product-image {
        height: 250px;
        object-fit: cover;
        transition: transform 0.3s ease;
    }
    
    .product-card:hover .product-image {
        transform: scale(1.05);
    }
    
    .product-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.5);
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    .product-card:hover .product-overlay {
        opacity: 1;
    }
</style>
@endpush

@push('scripts')
<script>
    // Add to cart functionality
    document.querySelectorAll('.add-to-cart-btn').forEach(button => {
        button.addEventListener('click', function() {
            const productId = this.dataset.productId;
            // Add to cart logic here
            console.log('Add to cart:', productId);
        });
    });
    
    // Wishlist functionality
    document.querySelectorAll('.wishlist-btn').forEach(button => {
        button.addEventListener('click', function() {
            const productId = this.dataset.productId;
            // Wishlist logic here
            console.log('Toggle wishlist:', productId);
        });
    });
</script>
@endpush
