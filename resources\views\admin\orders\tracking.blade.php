@extends('layouts.admin')

@section('title', 'Order Tracking Management')

@section('content')
<!-- <PERSON> Header -->
<div class="row align-items-center mb-3 mb-md-4">
    <div class="col-12 col-md-6">
        <h1 class="h4 h-md-3 mb-1 text-gray-800">Order Tracking</h1>
        <p class="text-muted mb-0 d-none d-md-block">Manage and track all orders</p>
    </div>
    <div class="col-12 col-md-6 mt-2 mt-md-0">
        <div class="d-flex flex-column flex-sm-row gap-2 justify-content-md-end">
            <button class="btn btn-success btn-sm" onclick="exportOrders()">
                <i class="fas fa-download me-1"></i>
                <span class="d-none d-sm-inline">Export Orders</span>
                <span class="d-sm-none">Export</span>
            </button>
            <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#bulkUpdateModal">
                <i class="fas fa-edit me-1"></i>
                <span class="d-none d-sm-inline">Bulk Update</span>
                <span class="d-sm-none">Bulk</span>
            </button>
        </div>
    </div>
</div>

<!-- Status Overview Cards -->
<div class="row g-3 g-md-4 mb-3 mb-md-4">
    <div class="col-6 col-lg-3">
        <div class="card border-left-warning shadow h-100">
            <div class="card-body p-3">
                <div class="d-flex align-items-center">
                    <div class="flex-fill me-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1 small">Pending</div>
                        <div class="h6 h-md-5 mb-0 font-weight-bold text-gray-800">{{ $statusCounts['pending'] ?? 0 }}</div>
                    </div>
                    <div class="text-warning">
                        <i class="fas fa-clock fa-lg fa-md-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-6 col-lg-3">
        <div class="card border-left-info shadow h-100">
            <div class="card-body p-3">
                <div class="d-flex align-items-center">
                    <div class="flex-fill me-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1 small">Processing</div>
                        <div class="h6 h-md-5 mb-0 font-weight-bold text-gray-800">{{ $statusCounts['processing'] ?? 0 }}</div>
                    </div>
                    <div class="text-info">
                        <i class="fas fa-cogs fa-lg fa-md-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-6 col-lg-3">
        <div class="card border-left-primary shadow h-100">
            <div class="card-body p-3">
                <div class="d-flex align-items-center">
                    <div class="flex-fill me-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1 small">Shipped</div>
                        <div class="h6 h-md-5 mb-0 font-weight-bold text-gray-800">{{ $statusCounts['shipped'] ?? 0 }}</div>
                    </div>
                    <div class="text-primary">
                        <i class="fas fa-shipping-fast fa-lg fa-md-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-6 col-lg-3">
        <div class="card border-left-success shadow h-100">
            <div class="card-body p-3">
                <div class="d-flex align-items-center">
                    <div class="flex-fill me-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1 small">Delivered</div>
                        <div class="h6 h-md-5 mb-0 font-weight-bold text-gray-800">{{ $statusCounts['delivered'] ?? 0 }}</div>
                    </div>
                    <div class="text-success">
                        <i class="fas fa-check-circle fa-lg fa-md-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="card shadow mb-3 mb-md-4">
    <div class="card-header py-2 py-md-3">
        <h6 class="m-0 font-weight-bold text-primary">
            <i class="fas fa-filter me-2"></i>Filter Orders
        </h6>
    </div>
    <div class="card-body p-3">
        <form method="GET" action="{{ route('admin.orders.tracking.index') }}">
            <!-- Mobile Layout -->
            <div class="d-md-none">
                <div class="row g-2 mb-3">
                    <div class="col-6">
                        <label class="form-label small">Status</label>
                        <select name="status" class="form-select form-select-sm">
                            <option value="">All</option>
                            <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                            <option value="confirmed" {{ request('status') == 'confirmed' ? 'selected' : '' }}>Confirmed</option>
                            <option value="processing" {{ request('status') == 'processing' ? 'selected' : '' }}>Processing</option>
                            <option value="packed" {{ request('status') == 'packed' ? 'selected' : '' }}>Packed</option>
                            <option value="shipped" {{ request('status') == 'shipped' ? 'selected' : '' }}>Shipped</option>
                            <option value="out_for_delivery" {{ request('status') == 'out_for_delivery' ? 'selected' : '' }}>Out for Delivery</option>
                            <option value="delivered" {{ request('status') == 'delivered' ? 'selected' : '' }}>Delivered</option>
                        </select>
                    </div>
                    <div class="col-6">
                        <label class="form-label small">Order Number</label>
                        <input type="text" name="order_number" class="form-control form-control-sm" value="{{ request('order_number') }}" placeholder="Order #">
                    </div>
                </div>
                <div class="row g-2 mb-3">
                    <div class="col-6">
                        <label class="form-label small">From Date</label>
                        <input type="date" name="date_from" class="form-control form-control-sm" value="{{ request('date_from') }}">
                    </div>
                    <div class="col-6">
                        <label class="form-label small">To Date</label>
                        <input type="date" name="date_to" class="form-control form-control-sm" value="{{ request('date_to') }}">
                    </div>
                </div>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary btn-sm">
                        <i class="fas fa-search me-1"></i>Filter
                    </button>
                </div>
            </div>

            <!-- Desktop Layout -->
            <div class="row g-3 d-none d-md-flex">
                <div class="col-md-3">
                    <label class="form-label">Status</label>
                    <select name="status" class="form-select">
                        <option value="">All Statuses</option>
                        <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                        <option value="confirmed" {{ request('status') == 'confirmed' ? 'selected' : '' }}>Confirmed</option>
                        <option value="processing" {{ request('status') == 'processing' ? 'selected' : '' }}>Processing</option>
                        <option value="packed" {{ request('status') == 'packed' ? 'selected' : '' }}>Packed</option>
                        <option value="shipped" {{ request('status') == 'shipped' ? 'selected' : '' }}>Shipped</option>
                        <option value="out_for_delivery" {{ request('status') == 'out_for_delivery' ? 'selected' : '' }}>Out for Delivery</option>
                        <option value="delivered" {{ request('status') == 'delivered' ? 'selected' : '' }}>Delivered</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">Order Number</label>
                    <input type="text" name="order_number" class="form-control" value="{{ request('order_number') }}" placeholder="Search order number">
                </div>
                <div class="col-md-2">
                    <label class="form-label">From Date</label>
                    <input type="date" name="date_from" class="form-control" value="{{ request('date_from') }}">
                </div>
                <div class="col-md-2">
                    <label class="form-label">To Date</label>
                    <input type="date" name="date_to" class="form-control" value="{{ request('date_to') }}">
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>Filter
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Orders Table -->
<div class="card shadow mb-3 mb-md-4">
    <div class="card-header py-2 py-md-3 d-flex justify-content-between align-items-center">
        <h6 class="m-0 font-weight-bold text-primary">
            <i class="fas fa-list me-2"></i>Orders
        </h6>
        <small class="text-muted d-none d-md-inline">{{ $orders->total() ?? 0 }} total</small>
    </div>
    <div class="card-body p-0">
        <!-- Mobile Order Cards -->
        <div class="d-md-none">
            @forelse($orders ?? [] as $order)
            <div class="border-bottom p-3">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <div class="d-flex align-items-center">
                        <input type="checkbox" class="order-checkbox me-2" value="{{ $order->id ?? 1 }}">
                        <div>
                            <h6 class="mb-0">
                                <a href="{{ route('admin.orders.tracking.show', $order->id ?? 1) }}" class="text-decoration-none">
                                    {{ $order->order_number ?? 'ORD-2024-001' }}
                                </a>
                            </h6>
                            <small class="text-muted">{{ $order->user->name ?? 'John Doe' }}</small>
                        </div>
                    </div>
                    <span class="badge bg-primary">{{ $order->getStatusDisplayName() ?? 'Pending' }}</span>
                </div>
                <div class="row g-2 mb-2">
                    <div class="col-6">
                        <small class="text-muted d-block">Total</small>
                        <strong>₹{{ number_format($order->total_amount ?? 15000, 0) }}</strong>
                    </div>
                    <div class="col-6">
                        <small class="text-muted d-block">Date</small>
                        <small>{{ $order->created_at->format('M d, Y') ?? date('M d, Y') }}</small>
                    </div>
                </div>
                @if($order->tracking_number ?? false)
                <div class="mb-2">
                    <small class="text-muted d-block">Tracking</small>
                    <a href="{{ $order->tracking_url ?? '#' }}" target="_blank" class="text-decoration-none small">
                        {{ $order->tracking_number }}
                    </a>
                    <small class="text-muted ms-2">{{ $order->courier_service ?? 'N/A' }}</small>
                </div>
                @endif
                <div class="d-flex gap-1">
                    <button class="btn btn-outline-primary btn-sm" onclick="viewOrder({{ $order->id ?? 1 }})">
                        <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-outline-success btn-sm" onclick="updateStatus({{ $order->id ?? 1 }})">
                        <i class="fas fa-edit"></i>
                    </button>
                    @if(!($order->tracking_number ?? false))
                    <button class="btn btn-outline-info btn-sm" onclick="addTracking({{ $order->id ?? 1 }})">
                        <i class="fas fa-plus"></i>
                    </button>
                    @endif
                </div>
            </div>
            @empty
            <div class="text-center py-4">
                <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                <p class="text-muted mb-0">No orders found</p>
            </div>
            @endforelse
        </div>

        <!-- Desktop Table -->
        <div class="table-responsive d-none d-md-block">
            <table class="table table-bordered mb-0" id="ordersTable">
                <thead>
                    <tr>
                        <th><input type="checkbox" id="selectAll"></th>
                        <th>Order #</th>
                        <th>Customer</th>
                        <th>Status</th>
                        <th>Total</th>
                        <th>Tracking #</th>
                        <th>Courier</th>
                        <th>Order Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    @forelse($orders ?? [] as $order)
                    <tr>
                        <td><input type="checkbox" class="order-checkbox" value="{{ $order->id ?? 1 }}"></td>
                        <td>
                            <a href="{{ route('admin.orders.tracking.show', $order->id ?? 1) }}" class="text-decoration-none">
                                {{ $order->order_number ?? 'ORD-2024-001' }}
                            </a>
                        </td>
                        <td>
                            <div>{{ $order->user->name ?? 'Unknown User' }}</div>
                            <small class="text-muted">{{ $order->user->email ?? 'N/A' }}</small>
                        </td>
                        <td>
                            <span class="badge bg-primary">
                                {{ $order->getStatusDisplayName() ?? 'Pending' }}
                            </span>
                        </td>
                        <td>₹{{ number_format($order->total_amount ?? 15000, 0) }}</td>
                        <td>
                            @if($order->tracking_number ?? false)
                                <a href="{{ $order->tracking_url ?? '#' }}" target="_blank" class="text-decoration-none">
                                    {{ $order->tracking_number }}
                                </a>
                            @else
                                <span class="text-muted">Not assigned</span>
                            @endif
                        </td>
                        <td>{{ $order->courier_service ?? 'N/A' }}</td>
                        <td>{{ $order->created_at->format('M d, Y') ?? date('M d, Y') }}</td>
                        <td>
                            <div class="btn-group" role="group">
                                <button class="btn btn-sm btn-outline-primary" onclick="viewOrder({{ $order->id ?? 1 }})">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-success" onclick="updateStatus({{ $order->id ?? 1 }})">
                                    <i class="fas fa-edit"></i>
                                </button>
                                @if(!($order->tracking_number ?? false))
                                <button class="btn btn-sm btn-outline-info" onclick="addTracking({{ $order->id ?? 1 }})">
                                    <i class="fas fa-plus"></i>
                                </button>
                                @endif
                            </div>
                        </td>
                    </tr>
                    @empty
                    <tr>
                        <td colspan="9" class="text-center py-4">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No orders found</p>
                        </td>
                    </tr>
                    @endforelse
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        @if(isset($orders) && $orders->hasPages())
        <div class="d-flex justify-content-center mt-3 mt-md-4 px-3">
            {{ $orders->appends(request()->query())->links() }}
        </div>
        @endif
    </div>
</div>

<!-- Update Status Modal -->
<div class="modal fade" id="updateStatusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Update Order Status</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="updateStatusForm">
                    <input type="hidden" id="updateOrderId">
                    <div class="mb-3">
                        <label class="form-label">New Status</label>
                        <select id="newStatus" class="form-select" required>
                            <option value="">Select Status</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Notes</label>
                        <textarea id="statusNotes" class="form-control" rows="3" placeholder="Optional notes about this status update"></textarea>
                    </div>
                    <div id="deliveryFields" style="display: none;">
                        <div class="mb-3">
                            <label class="form-label">Delivery Confirmation Method</label>
                            <select id="deliveryMethod" class="form-select">
                                <option value="">Select Method</option>
                                <option value="sms">SMS</option>
                                <option value="email">Email</option>
                                <option value="call">Phone Call</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Delivered To</label>
                            <input type="text" id="deliveredTo" class="form-control" placeholder="Name of person who received">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Delivery Notes</label>
                            <textarea id="deliveryNotes" class="form-control" rows="2" placeholder="Additional delivery information"></textarea>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="submitStatusUpdate()">Update Status</button>
            </div>
        </div>
    </div>
</div>

<!-- Add Tracking Modal -->
<div class="modal fade" id="addTrackingModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Tracking Information</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addTrackingForm">
                    <input type="hidden" id="trackingOrderId">
                    <div class="mb-3">
                        <label class="form-label">Tracking Number</label>
                        <input type="text" id="trackingNumber" class="form-control" required placeholder="Enter tracking number">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Courier Service</label>
                        <select id="courierService" class="form-select" required>
                            <option value="India Post">India Post</option>
                            <option value="Speed Post">Speed Post</option>
                            <option value="Blue Dart">Blue Dart</option>
                            <option value="DTDC">DTDC</option>
                            <option value="FedEx">FedEx</option>
                            <option value="DHL">DHL</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Notes</label>
                        <textarea id="trackingNotes" class="form-control" rows="3" placeholder="Optional notes"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="submitTrackingInfo()">Add Tracking</button>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Update Modal -->
<div class="modal fade" id="bulkUpdateModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Bulk Update Orders</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="bulkUpdateForm">
                    <div class="mb-3">
                        <label class="form-label">New Status</label>
                        <select id="bulkStatus" class="form-select" required>
                            <option value="">Select Status</option>
                            <option value="confirmed">Confirmed</option>
                            <option value="processing">Processing</option>
                            <option value="packed">Packed</option>
                            <option value="shipped">Shipped</option>
                            <option value="out_for_delivery">Out for Delivery</option>
                            <option value="delivered">Delivered</option>
                            <option value="cancelled">Cancelled</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Notes</label>
                        <textarea id="bulkNotes" class="form-control" rows="3" placeholder="Optional notes for all selected orders"></textarea>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <span id="selectedCount">0</span> orders selected for bulk update.
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="submitBulkUpdate()">Update Selected Orders</button>
            </div>
        </div>
    </div>
</div>
@push('styles')
<style>
    /* Order Tracking Mobile-First Styles */

    /* Mobile Status Cards */
    @media (max-width: 575.98px) {
        .h6 {
            font-size: 1rem !important;
        }

        .fa-lg {
            font-size: 1.2rem !important;
        }

        .small {
            font-size: 0.75rem !important;
        }
    }

    /* Mobile Filter Form */
    @media (max-width: 767.98px) {
        .form-select-sm,
        .form-control-sm {
            font-size: 0.85rem;
            padding: 0.375rem 0.5rem;
        }

        .btn-sm {
            padding: 0.375rem 0.75rem;
            font-size: 0.85rem;
        }
    }

    /* Mobile Order Cards */
    .mobile-order-card {
        transition: all 0.3s ease;
        border-radius: 10px;
    }

    .mobile-order-card:hover {
        background-color: #f8f9fa;
        transform: translateY(-1px);
    }

    @media (max-width: 767.98px) {
        .border-bottom {
            border-bottom: 1px solid #e9ecef !important;
        }

        .border-bottom:last-child {
            border-bottom: none !important;
        }

        .btn-group .btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
        }
    }

    /* Mobile Table Improvements */
    @media (max-width: 767.98px) {
        .table-responsive {
            font-size: 0.85rem;
        }

        .table th,
        .table td {
            padding: 0.5rem 0.25rem;
            vertical-align: middle;
        }

        .btn-group {
            flex-direction: column;
            gap: 2px;
        }

        .btn-group .btn {
            border-radius: 4px !important;
        }
    }

    /* Mobile Modal Improvements */
    @media (max-width: 767.98px) {
        .modal-dialog {
            margin: 0.5rem;
        }

        .modal-header {
            padding: 1rem;
        }

        .modal-body {
            padding: 1rem;
        }

        .modal-footer {
            padding: 1rem;
            gap: 0.5rem;
        }

        .modal-footer .btn {
            flex: 1;
        }
    }

    /* Mobile Badge Improvements */
    @media (max-width: 767.98px) {
        .badge {
            font-size: 0.7rem;
            padding: 0.3rem 0.6rem;
        }
    }

    /* Mobile Checkbox Improvements */
    @media (max-width: 767.98px) {
        input[type="checkbox"] {
            width: 18px;
            height: 18px;
        }
    }

    /* Mobile Pagination */
    @media (max-width: 767.98px) {
        .pagination {
            justify-content: center;
        }

        .page-link {
            padding: 0.375rem 0.75rem;
            font-size: 0.85rem;
        }

        .page-item:not(.active) .page-link {
            display: none;
        }

        .page-item.active .page-link,
        .page-item:first-child .page-link,
        .page-item:last-child .page-link,
        .page-item:nth-child(2) .page-link,
        .page-item:nth-last-child(2) .page-link {
            display: block;
        }
    }

    /* Loading States */
    .loading {
        opacity: 0.6;
        pointer-events: none;
    }

    .loading::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 20px;
        height: 20px;
        margin: -10px 0 0 -10px;
        border: 2px solid #f3f3f3;
        border-top: 2px solid var(--primary-pink);
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Touch-friendly improvements */
    @media (max-width: 767.98px) {
        .btn {
            min-height: 44px;
        }

        .btn-sm {
            min-height: 36px;
        }

        .form-control,
        .form-select {
            min-height: 44px;
        }

        .form-control-sm,
        .form-select-sm {
            min-height: 36px;
        }
    }

    /* Card hover effects */
    .card {
        transition: all 0.3s ease;
    }

    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
    }

    /* Status badge colors */
    .badge.bg-warning { background-color: #ffc107 !important; }
    .badge.bg-info { background-color: #0dcaf0 !important; }
    .badge.bg-primary { background-color: var(--primary-pink) !important; }
    .badge.bg-success { background-color: #198754 !important; }
    .badge.bg-danger { background-color: #dc3545 !important; }

    /* Mobile notification positioning */
    @media (max-width: 767.98px) {
        .alert.position-fixed {
            top: 70px !important;
            left: 10px !important;
            right: 10px !important;
            width: auto !important;
            min-width: auto !important;
        }
    }
</style>
@endpush

@push('scripts')
<script>
// Global variables
let currentOrderId = null;

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Select all checkbox functionality
    document.getElementById('selectAll').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.order-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateSelectedCount();
    });

    // Individual checkbox change
    document.querySelectorAll('.order-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedCount);
    });

    // Status change handler
    document.getElementById('newStatus').addEventListener('change', function() {
        const deliveryFields = document.getElementById('deliveryFields');
        if (this.value === 'delivered') {
            deliveryFields.style.display = 'block';
        } else {
            deliveryFields.style.display = 'none';
        }
    });
});

function updateSelectedCount() {
    const selectedCheckboxes = document.querySelectorAll('.order-checkbox:checked');
    document.getElementById('selectedCount').textContent = selectedCheckboxes.length;
}

function viewOrder(orderId) {
    window.open(`/admin/orders/tracking/${orderId}`, '_blank');
}

function updateStatus(orderId) {
    currentOrderId = orderId;

    // Fetch current order details and possible statuses
    fetch(`/admin/orders/tracking/${orderId}/info`)
        .then(response => response.json())
        .then(data => {
            document.getElementById('updateOrderId').value = orderId;

            // Populate status options based on current status
            const statusSelect = document.getElementById('newStatus');
            statusSelect.innerHTML = '<option value="">Select Status</option>';

            const possibleStatuses = {
                'pending': ['confirmed', 'cancelled'],
                'confirmed': ['processing', 'cancelled'],
                'processing': ['packed', 'cancelled'],
                'packed': ['shipped'],
                'shipped': ['out_for_delivery', 'delivered'],
                'out_for_delivery': ['delivered'],
                'delivered': [],
                'cancelled': [],
                'refunded': []
            };

            const statusNames = {
                'confirmed': 'Confirmed',
                'processing': 'Processing',
                'packed': 'Packed',
                'shipped': 'Shipped',
                'out_for_delivery': 'Out for Delivery',
                'delivered': 'Delivered',
                'cancelled': 'Cancelled'
            };

            const allowedStatuses = possibleStatuses[data.status] || [];
            allowedStatuses.forEach(status => {
                const option = document.createElement('option');
                option.value = status;
                option.textContent = statusNames[status];
                statusSelect.appendChild(option);
            });

            // Show modal
            new bootstrap.Modal(document.getElementById('updateStatusModal')).show();
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('Error fetching order details', 'danger');
        });
}

function submitStatusUpdate() {
    const orderId = document.getElementById('updateOrderId').value;
    const status = document.getElementById('newStatus').value;
    const notes = document.getElementById('statusNotes').value;
    const deliveryMethod = document.getElementById('deliveryMethod').value;
    const deliveredTo = document.getElementById('deliveredTo').value;
    const deliveryNotes = document.getElementById('deliveryNotes').value;

    if (!status) {
        showAlert('Please select a status', 'warning');
        return;
    }

    const data = {
        status: status,
        notes: notes,
        delivery_confirmation_method: deliveryMethod,
        delivered_to: deliveredTo,
        delivery_notes: deliveryNotes
    };

    fetch(`/admin/orders/tracking/${orderId}/status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(data.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('updateStatusModal')).hide();
            location.reload(); // Refresh page to show updated status
        } else {
            showAlert(data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('Error updating status', 'danger');
    });
}

function addTracking(orderId) {
    document.getElementById('trackingOrderId').value = orderId;
    new bootstrap.Modal(document.getElementById('addTrackingModal')).show();
}

function submitTrackingInfo() {
    const orderId = document.getElementById('trackingOrderId').value;
    const trackingNumber = document.getElementById('trackingNumber').value;
    const courierService = document.getElementById('courierService').value;
    const notes = document.getElementById('trackingNotes').value;

    if (!trackingNumber) {
        showAlert('Please enter a tracking number', 'warning');
        return;
    }

    const data = {
        tracking_number: trackingNumber,
        courier_service: courierService,
        notes: notes
    };

    fetch(`/admin/orders/tracking/${orderId}/tracking`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(data.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('addTrackingModal')).hide();
            location.reload(); // Refresh page to show tracking info
        } else {
            showAlert(data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('Error adding tracking information', 'danger');
    });
}

function submitBulkUpdate() {
    const selectedCheckboxes = document.querySelectorAll('.order-checkbox:checked');
    const orderIds = Array.from(selectedCheckboxes).map(cb => cb.value);
    const status = document.getElementById('bulkStatus').value;
    const notes = document.getElementById('bulkNotes').value;

    if (orderIds.length === 0) {
        showAlert('Please select at least one order', 'warning');
        return;
    }

    if (!status) {
        showAlert('Please select a status', 'warning');
        return;
    }

    const data = {
        order_ids: orderIds,
        status: status,
        notes: notes
    };

    fetch('/admin/orders/tracking/bulk-update', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(data.message, 'success');
            bootstrap.Modal.getInstance(document.getElementById('bulkUpdateModal')).hide();
            location.reload(); // Refresh page to show updated statuses
        } else {
            showAlert(data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('Error updating orders', 'danger');
    });
}

function exportOrders() {
    const params = new URLSearchParams(window.location.search);
    window.location.href = '/admin/orders/tracking/export?' + params.toString();
}

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
@endpush

@endsection
