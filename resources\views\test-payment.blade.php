<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Razorpay Payment</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h4>Test Razorpay Payment</h4>
                    </div>
                    <div class="card-body">
                        <h5>Order Details</h5>
                        <p><strong>Order Number:</strong> {{ $order->order_number }}</p>
                        <p><strong>Amount:</strong> ₹{{ number_format($order->total_amount, 2) }}</p>
                        <p><strong>Status:</strong> {{ $order->status }}</p>
                        
                        <button id="payButton" class="btn btn-primary btn-lg w-100">
                            Pay with Razorpay
                        </button>
                        
                        <div id="result" class="mt-3"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://checkout.razorpay.com/v1/checkout.js"></script>
    <script>
        document.getElementById('payButton').addEventListener('click', function() {
            // Create Razorpay order
            fetch('/payment/create-order', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    order_id: {{ $order->id }}
                })
            })
            .then(response => response.json())
            .then(data => {
                console.log('Payment initialization response:', data);
                
                if (data.success) {
                    const options = {
                        key: data.key_id,
                        amount: data.amount,
                        currency: data.currency,
                        name: 'Shreeji Web Test',
                        description: 'Test Order #' + data.order_number,
                        order_id: data.order_id,
                        prefill: {
                            name: data.customer.name,
                            email: data.customer.email,
                            contact: data.customer.contact
                        },
                        theme: {
                            color: '#e91e63'
                        },
                        handler: function(response) {
                            console.log('Payment successful:', response);
                            
                            // Verify payment
                            fetch('/payment/verify', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                                },
                                body: JSON.stringify({
                                    razorpay_order_id: response.razorpay_order_id,
                                    razorpay_payment_id: response.razorpay_payment_id,
                                    razorpay_signature: response.razorpay_signature,
                                    order_id: {{ $order->id }}
                                })
                            })
                            .then(response => response.json())
                            .then(data => {
                                if (data.success) {
                                    document.getElementById('result').innerHTML = 
                                        '<div class="alert alert-success">Payment successful! Order confirmed.</div>';
                                } else {
                                    document.getElementById('result').innerHTML = 
                                        '<div class="alert alert-danger">Payment verification failed: ' + data.message + '</div>';
                                }
                            });
                        },
                        modal: {
                            ondismiss: function() {
                                document.getElementById('result').innerHTML = 
                                    '<div class="alert alert-warning">Payment cancelled</div>';
                            }
                        }
                    };

                    const rzp = new Razorpay(options);
                    rzp.open();
                } else {
                    document.getElementById('result').innerHTML = 
                        '<div class="alert alert-danger">Failed to initialize payment: ' + data.message + '</div>';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                document.getElementById('result').innerHTML = 
                    '<div class="alert alert-danger">Error: ' + error.message + '</div>';
            });
        });
    </script>
</body>
</html>
