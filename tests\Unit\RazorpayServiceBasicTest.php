<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Services\RazorpayService;

class RazorpayServiceBasicTest extends TestCase
{
    /** @test */
    public function it_can_instantiate_razorpay_service()
    {
        // Set test environment variables
        config([
            'services.razorpay.key_id' => 'rzp_test_key',
            'services.razorpay.key_secret' => 'test_secret',
            'services.razorpay.webhook_secret' => 'webhook_secret'
        ]);

        $service = new RazorpayService();
        
        $this->assertInstanceOf(RazorpayService::class, $service);
    }

    /** @test */
    public function it_can_get_test_card_details()
    {
        // Set test environment variables
        config([
            'services.razorpay.key_id' => 'rzp_test_key',
            'services.razorpay.key_secret' => 'test_secret',
            'services.razorpay.webhook_secret' => 'webhook_secret'
        ]);

        $service = new RazorpayService();
        $result = $service->getTestCardDetails();

        $this->assertIsArray($result);
        $this->assertArrayHasKey('success_cards', $result);
        $this->assertArrayHasKey('failure_cards', $result);
    }

    /** @test */
    public function it_has_required_configuration()
    {
        // Set test environment variables
        config([
            'services.razorpay.key_id' => 'rzp_test_key',
            'services.razorpay.key_secret' => 'test_secret',
            'services.razorpay.webhook_secret' => 'webhook_secret'
        ]);

        $this->assertEquals('rzp_test_key', config('services.razorpay.key_id'));
        $this->assertEquals('test_secret', config('services.razorpay.key_secret'));
        $this->assertEquals('webhook_secret', config('services.razorpay.webhook_secret'));
    }
}
