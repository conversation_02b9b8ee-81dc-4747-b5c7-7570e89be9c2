<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use App\Models\OtpVerification;

class MockSmsService
{
    /**
     * Send OTP via mock SMS (for testing without <PERSON><PERSON><PERSON>)
     */
    public function sendOtp($phone, $otpCode, $purpose = 'login')
    {
        try {
            // Format phone number
            $formattedPhone = $this->formatPhoneNumber($phone);
            
            // Create message
            $message = $this->createOtpMessage($otpCode, $purpose);

            // Log the SMS instead of sending it
            Log::info('MOCK SMS SENT', [
                'phone' => $formattedPhone,
                'message' => $message,
                'otp_code' => $otpCode,
                'purpose' => $purpose
            ]);

            // For development, also write to a file
            $logMessage = "[" . now()->format('Y-m-d H:i:s') . "] SMS to {$formattedPhone}: {$message}\n";
            file_put_contents(storage_path('logs/mock_sms.log'), $logMessage, FILE_APPEND | LOCK_EX);

            return [
                'success' => true,
                'message_sid' => 'mock_' . uniqid(),
                'message' => 'OTP sent successfully (MOCK MODE)'
            ];

        } catch (\Exception $e) {
            Log::error('Mock SMS failed', [
                'phone' => $phone,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'Failed to send OTP. Please try again.'
            ];
        }
    }

    /**
     * Send order confirmation SMS
     */
    public function sendOrderConfirmation($phone, $orderNumber, $customerName = null)
    {
        try {
            $formattedPhone = $this->formatPhoneNumber($phone);
            $message = $this->createOrderConfirmationMessage($orderNumber, $customerName);

            // Log the SMS instead of sending it
            Log::info('MOCK ORDER CONFIRMATION SMS', [
                'phone' => $formattedPhone,
                'message' => $message,
                'order_number' => $orderNumber
            ]);

            // Write to file
            $logMessage = "[" . now()->format('Y-m-d H:i:s') . "] Order SMS to {$formattedPhone}: {$message}\n";
            file_put_contents(storage_path('logs/mock_sms.log'), $logMessage, FILE_APPEND | LOCK_EX);

            return [
                'success' => true,
                'message_sid' => 'mock_order_' . uniqid(),
                'message' => 'Order confirmation sent successfully (MOCK MODE)'
            ];

        } catch (\Exception $e) {
            Log::error('Mock order confirmation SMS failed', [
                'phone' => $phone,
                'order_number' => $orderNumber,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'Failed to send order confirmation'
            ];
        }
    }

    /**
     * Send order status update SMS (mock)
     */
    public function sendOrderStatusUpdate($phone, $orderNumber, $status, $customerName = null, $trackingNumber = null)
    {
        try {
            $formattedPhone = $this->formatPhoneNumber($phone);
            $message = $this->createOrderStatusMessage($orderNumber, $status, $customerName, $trackingNumber);

            // Log the SMS instead of sending it
            Log::info('MOCK ORDER STATUS SMS SENT', [
                'phone' => $formattedPhone,
                'order_number' => $orderNumber,
                'status' => $status,
                'tracking_number' => $trackingNumber,
                'message' => $message
            ]);

            return [
                'success' => true,
                'message_sid' => 'mock_' . uniqid(),
                'message' => 'Mock order status SMS sent successfully'
            ];

        } catch (\Exception $e) {
            Log::error('Mock order status SMS failed', [
                'phone' => $phone,
                'order_number' => $orderNumber,
                'status' => $status,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'Failed to send mock order status SMS'
            ];
        }
    }

    /**
     * Generate and send OTP (combines generation and sending)
     */
    public function generateAndSendOtp($phone, $purpose = 'login', $sessionId = null, $ipAddress = null)
    {
        // Generate OTP
        $otpResult = OtpVerification::generateOtp($phone, $purpose, $sessionId, $ipAddress);
        
        if (!$otpResult['success']) {
            return $otpResult;
        }

        // Send OTP via mock SMS
        $smsResult = $this->sendOtp($phone, $otpResult['otp_code'], $purpose);
        
        if (!$smsResult['success']) {
            // If SMS failed, delete the generated OTP
            OtpVerification::find($otpResult['otp_id'])?->delete();
            return $smsResult;
        }

        return [
            'success' => true,
            'message' => 'OTP sent successfully to your mobile number (MOCK MODE)',
            'otp_id' => $otpResult['otp_id'],
            'expires_at' => $otpResult['expires_at'],
            'message_sid' => $smsResult['message_sid'],
            'mock_otp' => $otpResult['otp_code'] // Include OTP for testing
        ];
    }

    /**
     * Format phone number for international format
     */
    protected function formatPhoneNumber($phone)
    {
        // Remove any non-digit characters
        $phone = preg_replace('/[^0-9]/', '', $phone);
        
        // If it starts with 91, add +
        if (substr($phone, 0, 2) === '91') {
            return '+' . $phone;
        }
        
        // If it's 10 digits, assume it's Indian number
        if (strlen($phone) === 10) {
            return '+91' . $phone;
        }
        
        // If it doesn't start with +, add +91
        if (substr($phone, 0, 1) !== '+') {
            return '+91' . $phone;
        }
        
        return $phone;
    }

    /**
     * Create OTP message based on purpose
     */
    protected function createOtpMessage($otpCode, $purpose)
    {
        $brandName = config('app.name', 'ShreeJi Jewelry');
        
        switch ($purpose) {
            case 'checkout':
                return "Your {$brandName} checkout verification code is: {$otpCode}. Valid for 5 minutes. Do not share this code.";
            case 'registration':
                return "Welcome to {$brandName}! Your verification code is: {$otpCode}. Valid for 5 minutes.";
            default:
                return "Your {$brandName} login verification code is: {$otpCode}. Valid for 5 minutes. Do not share this code.";
        }
    }

    /**
     * Create order confirmation message
     */
    protected function createOrderConfirmationMessage($orderNumber, $customerName = null)
    {
        $brandName = config('app.name', 'ShreeJi Jewelry');
        $greeting = $customerName ? "Dear {$customerName}, " : "";

        return "{$greeting}Thank you for your order! Your Order No: {$orderNumber}. We'll notify you once your jewelry is ready for delivery. - {$brandName}";
    }

    /**
     * Create order status update message
     */
    protected function createOrderStatusMessage($orderNumber, $status, $customerName = null, $trackingNumber = null)
    {
        $brandName = config('app.name', 'ShreeJi Jewelry');
        $greeting = $customerName ? "Dear {$customerName}, " : "";

        $statusMessages = [
            'confirmed' => 'Your order has been confirmed and is being prepared.',
            'processing' => 'Your order is being crafted with care.',
            'shipped' => 'Great news! Your order has been shipped.',
            'delivered' => 'Your order has been delivered. Thank you for choosing us!',
            'cancelled' => 'Your order has been cancelled. If you have any questions, please contact us.',
        ];

        $statusText = $statusMessages[$status] ?? "Your order status has been updated to: {$status}";

        $message = "{$greeting}Order Update - {$orderNumber}: {$statusText}";

        if ($trackingNumber && $status === 'shipped') {
            $message .= " Tracking: {$trackingNumber}";
        }

        $message .= " - {$brandName}";

        return $message;
    }

    /**
     * Check if service is configured (always true for mock)
     */
    public function isConfigured()
    {
        return true;
    }

    /**
     * Test connection (always successful for mock)
     */
    public function testConnection()
    {
        return [
            'success' => true,
            'account_sid' => 'mock_account',
            'account_status' => 'active',
            'message' => 'Mock SMS service connection successful'
        ];
    }
}
