<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Development Testing - ShreeJi Jewelry</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <meta name="csrf-token" content="{{ csrf_token() }}">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h3 class="mb-0"><i class="fas fa-tools me-2"></i>Development Testing Panel</h3>
                        <small>Environment: {{ config('app.env') }}</small>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            This page is only available in development mode to test the checkout process.
                        </div>

                        <h5>Checkout Flow Testing</h5>
                        <div class="row g-3 mb-4">
                            <div class="col-md-4">
                                <div class="card border-success">
                                    <div class="card-body text-center">
                                        <i class="fas fa-shopping-cart fa-2x text-success mb-2"></i>
                                        <h6>Step 1: Add to Cart</h6>
                                        <button class="btn btn-success" onclick="addToCart()">Add Sample Product</button>
                                        <div id="cart-status" class="mt-2"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card border-info">
                                    <div class="card-body text-center">
                                        <i class="fas fa-user fa-2x text-info mb-2"></i>
                                        <h6>Step 2: Quick Login</h6>
                                        <button class="btn btn-info" onclick="quickLogin()">Dev Login</button>
                                        <div id="login-status" class="mt-2"></div>
                                        <small class="d-block mt-1 text-muted">Bypasses OTP completely</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card border-warning">
                                    <div class="card-body text-center">
                                        <i class="fas fa-credit-card fa-2x text-warning mb-2"></i>
                                        <h6>Step 3: Checkout</h6>
                                        <a href="{{ route('checkout') }}" class="btn btn-warning">Go to Checkout</a>
                                        <small class="d-block mt-1 text-muted">Normal checkout flow</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <h5>Configuration Status</h5>
                        <div class="row g-3 mb-4">
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-body text-center">
                                        <i class="fas fa-sms fa-2x text-info mb-2"></i>
                                        <h6>SMS Service</h6>
                                        <span class="badge bg-warning">Mock Mode</span>
                                        <small class="d-block mt-1">Check logs/mock_sms.log</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-body text-center">
                                        <i class="fas fa-credit-card fa-2x text-primary mb-2"></i>
                                        <h6>Razorpay</h6>
                                        <button class="btn btn-sm btn-outline-primary" onclick="testRazorpay()">Test Config</button>
                                        <div id="razorpay-status" class="mt-2"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card">
                                    <div class="card-body text-center">
                                        <i class="fas fa-database fa-2x text-success mb-2"></i>
                                        <h6>Database</h6>
                                        <a href="{{ route('debug-products') }}" class="btn btn-sm btn-outline-success" target="_blank">Check Products</a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <h5>Quick Links</h5>
                        <div class="list-group">
                            <a href="{{ route('products') }}" class="list-group-item list-group-item-action">
                                <i class="fas fa-gem me-2"></i>Products Page
                            </a>
                            <a href="{{ route('cart') }}" class="list-group-item list-group-item-action">
                                <i class="fas fa-shopping-cart me-2"></i>Shopping Cart
                            </a>
                            <a href="{{ route('mobile.login') }}" class="list-group-item list-group-item-action">
                                <i class="fas fa-mobile-alt me-2"></i>Mobile Login (OTP)
                            </a>
                            <a href="{{ route('admin.dashboard') }}" class="list-group-item list-group-item-action">
                                <i class="fas fa-cog me-2"></i>Admin Dashboard
                            </a>
                        </div>

                        <div class="mt-4">
                            <h6>Development Tools:</h6>
                            <div class="d-flex gap-2 mb-3">
                                <button class="btn btn-sm btn-outline-danger" onclick="clearOtpRecords()">Clear OTP Records</button>
                                <button class="btn btn-sm btn-outline-info" onclick="window.location.reload()">Refresh Page</button>
                            </div>
                            <div id="dev-tools-status"></div>
                        </div>

                        <div class="mt-4">
                            <h6>Recent Changes:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>Fixed OTP rate limiting (more lenient for checkout)</li>
                                <li><i class="fas fa-check text-success me-2"></i>Added Razorpay configuration</li>
                                <li><i class="fas fa-check text-success me-2"></i>Improved checkout form validation</li>
                                <li><i class="fas fa-check text-success me-2"></i>Added development bypass for authentication</li>
                                <li><i class="fas fa-check text-success me-2"></i>Added quick login bypass</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function addToCart() {
            fetch('{{ route("dev.add-to-cart") }}')
                .then(response => response.json())
                .then(data => {
                    const statusDiv = document.getElementById('cart-status');
                    if (data.success) {
                        statusDiv.innerHTML = `<small class="text-success"><i class="fas fa-check me-1"></i>${data.message}<br>Cart items: ${data.cart_count}</small>`;
                    } else {
                        statusDiv.innerHTML = `<small class="text-danger"><i class="fas fa-times me-1"></i>${data.error}</small>`;
                    }
                })
                .catch(error => {
                    document.getElementById('cart-status').innerHTML = `<small class="text-danger">Error: ${error.message}</small>`;
                });
        }

        function testRazorpay() {
            fetch('{{ route("test.razorpay") }}')
                .then(response => response.json())
                .then(data => {
                    const statusDiv = document.getElementById('razorpay-status');
                    if (data.status === 'config_check') {
                        const keyStatus = data.razorpay_config.key_id ? 'SET' : 'NOT SET';
                        const secretStatus = data.razorpay_config.key_secret;
                        statusDiv.innerHTML = `<small>Key: ${keyStatus}<br>Secret: ${secretStatus}</small>`;
                    } else {
                        statusDiv.innerHTML = `<small class="text-danger">Error: ${data.message}</small>`;
                    }
                })
                .catch(error => {
                    document.getElementById('razorpay-status').innerHTML = `<small class="text-danger">Error: ${error.message}</small>`;
                });
        }

        function quickLogin() {
            const formData = new FormData();
            formData.append('phone', '9999999999');
            formData.append('name', 'Dev User');

            fetch('{{ route("dev.login") }}', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                const statusDiv = document.getElementById('login-status');
                if (data.success) {
                    statusDiv.innerHTML = `<small class="text-success"><i class="fas fa-check me-1"></i>Logged in as ${data.user.name}</small>`;
                    // Update checkout button to show it's ready
                    setTimeout(() => {
                        window.location.reload(); // Refresh to show logged in state
                    }, 1000);
                } else {
                    statusDiv.innerHTML = `<small class="text-danger"><i class="fas fa-times me-1"></i>${data.message}</small>`;
                }
            })
            .catch(error => {
                document.getElementById('login-status').innerHTML = `<small class="text-danger">Error: ${error.message}</small>`;
            });
        }

        function clearOtpRecords() {
            fetch('{{ route("dev.clear-otp") }}')
                .then(response => response.json())
                .then(data => {
                    const statusDiv = document.getElementById('dev-tools-status');
                    if (data.success) {
                        statusDiv.innerHTML = `<small class="text-success"><i class="fas fa-check me-1"></i>${data.message}</small>`;
                    } else {
                        statusDiv.innerHTML = `<small class="text-danger"><i class="fas fa-times me-1"></i>Failed to clear OTP records</small>`;
                    }
                })
                .catch(error => {
                    document.getElementById('dev-tools-status').innerHTML = `<small class="text-danger">Error: ${error.message}</small>`;
                });
        }
    </script>
</body>
</html>
