<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $user_id
 * @property string $session_id
 * @property int $product_id
 * @property int $quantity
 * @property string $size
 * @property float $total_price
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon $updated_at
 *
 * @property-read \App\Models\User $user
 * @property-read \App\Models\Product $product
 *
 * @method static \Illuminate\Database\Eloquent\Collection getCartItems(int $userId = null, string $sessionId = null)
 * @method static float getCartTotal(int $userId = null, string $sessionId = null)
 * @method static int getCartCount(int $userId = null, string $sessionId = null)
 */
class CartItem extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'session_id',
        'product_id',
        'quantity',
        'size',
        'options',
    ];

    protected $casts = [
        'options' => 'array',
    ];

    // Relationships
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function product()
    {
        return $this->belongsTo(Product::class);
    }

    // Helper methods
    public function getTotalPriceAttribute()
    {
        return $this->product->current_price * $this->quantity;
    }

    public static function getCartItems($userId = null, $sessionId = null)
    {
        $query = static::with('product');

        if ($userId) {
            $query->where('user_id', $userId);
        } elseif ($sessionId) {
            $query->where('session_id', $sessionId);
        }

        return $query->get();
    }

    public static function getCartTotal($userId = null, $sessionId = null)
    {
        $items = static::getCartItems($userId, $sessionId);
        return $items->sum('total_price');
    }

    public static function getCartCount($userId = null, $sessionId = null)
    {
        $query = static::query();

        if ($userId) {
            $query->where('user_id', $userId);
        } elseif ($sessionId) {
            $query->where('session_id', $sessionId);
        }

        return $query->sum('quantity');
    }

    /**
     * Transfer session cart to user cart when user logs in
     */
    public static function transferSessionCartToUser($userId, $sessionId)
    {
        if (!$userId || !$sessionId) {
            return false;
        }

        // Get session cart items
        $sessionItems = static::where('session_id', $sessionId)->get();

        if ($sessionItems->isEmpty()) {
            return true; // Nothing to transfer
        }

        foreach ($sessionItems as $sessionItem) {
            // Check if user already has this product in cart
            $existingUserItem = static::where('user_id', $userId)
                ->where('product_id', $sessionItem->product_id)
                ->where('size', $sessionItem->size)
                ->first();

            if ($existingUserItem) {
                // Merge quantities
                $existingUserItem->update([
                    'quantity' => $existingUserItem->quantity + $sessionItem->quantity
                ]);
            } else {
                // Transfer session item to user
                $sessionItem->update([
                    'user_id' => $userId,
                    'session_id' => null
                ]);
            }
        }

        // Delete any remaining session items
        static::where('session_id', $sessionId)->delete();

        return true;
    }

    /**
     * Clear cart for user or session
     */
    public static function clearCart($userId = null, $sessionId = null)
    {
        $query = static::query();

        if ($userId) {
            $query->where('user_id', $userId);
        } elseif ($sessionId) {
            $query->where('session_id', $sessionId);
        }

        return $query->delete();
    }

    /**
     * Get cart summary with totals
     */
    public static function getCartSummary($userId = null, $sessionId = null)
    {
        $items = static::getCartItems($userId, $sessionId);

        $subtotal = 0;
        $discount = 0;
        $itemCount = 0;

        foreach ($items as $item) {
            $itemPrice = $item->product->isOnSale() ? $item->product->sale_price : $item->product->price;
            $itemTotal = $itemPrice * $item->quantity;
            $subtotal += $itemTotal;
            $itemCount += $item->quantity;

            // Calculate discount if product is on sale
            if ($item->product->isOnSale()) {
                $originalTotal = $item->product->price * $item->quantity;
                $discount += ($originalTotal - $itemTotal);
            }
        }

        // Calculate shipping (free shipping over ₹25,000)
        $shipping = $subtotal >= 25000 ? 0 : 500;

        // Calculate tax (3% GST)
        $tax = ($subtotal - $discount) * 0.03;

        // Calculate total
        $total = $subtotal - $discount + $shipping + $tax;

        return [
            'items' => $items,
            'item_count' => $itemCount,
            'subtotal' => $subtotal,
            'discount' => $discount,
            'shipping' => $shipping,
            'tax' => $tax,
            'total' => $total,
            'free_shipping_eligible' => $subtotal >= 25000,
            'free_shipping_remaining' => max(0, 25000 - $subtotal)
        ];
    }
}
