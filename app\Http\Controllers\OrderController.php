<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\CartItem;
use App\Models\User;
use App\Services\TwilioService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class OrderController extends Controller
{
    protected $twilioService;

    public function __construct(TwilioService $twilioService)
    {
        $this->twilioService = $twilioService;
        // Remove auth middleware from checkout - we'll handle authentication in the method
        $this->middleware('auth')->except(['checkout', 'guestCheckout']);
    }

    public function index()
    {
        $orders = Auth::user()->orders()
            ->with('orderItems.product')
            ->recent()
            ->paginate(10);

        return view('user.orders', compact('orders'));
    }

    public function show($id)
    {
        $order = Auth::user()->orders()
            ->with('orderItems.product')
            ->findOrFail($id);

        return view('user.order-detail', compact('order'));
    }

    public function checkout()
    {
        // Get cart items for authenticated user or guest session
        $userId = Auth::id();
        $sessionId = session()->getId();
        $cartItems = CartItem::getCartItems($userId, $sessionId);

        if ($cartItems->isEmpty()) {
            return redirect()->route('cart')->with('error', 'Your cart is empty.');
        }

        // If user is authenticated, proceed to checkout
        if (Auth::check()) {
            $cartSummary = CartItem::getCartSummary($userId);
            $user = Auth::user();
            return view('checkout.index', compact('cartItems', 'cartSummary', 'user'));
        }

        // In development mode, allow bypass of authentication for testing
        if (config('app.env') === 'local' && request()->has('bypass_auth')) {
            // Create a temporary guest user for testing
            $guestUser = (object) [
                'id' => null,
                'name' => 'Guest User',
                'email' => '<EMAIL>',
                'phone' => '9999999999'
            ];
            $cartSummary = CartItem::getCartSummary(null, $sessionId);
            return view('checkout.index', compact('cartItems', 'cartSummary', 'user'))->with('user', $guestUser);
        }

        // For guest users, show mobile authentication first
        return $this->guestCheckout();
    }

    /**
     * Guest checkout - prompt for mobile number and OTP
     */
    public function guestCheckout()
    {
        $userId = null;
        $sessionId = session()->getId();
        $cartSummary = CartItem::getCartSummary($userId, $sessionId);

        if ($cartSummary['items']->isEmpty()) {
            return redirect()->route('cart')->with('error', 'Your cart is empty.');
        }

        return view('checkout.guest', compact('cartSummary'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'billing_address' => 'required|array',
            'billing_address.name' => 'required|string|max:255',
            'billing_address.email' => 'required|email',
            'billing_address.phone' => 'required|string',
            'billing_address.address' => 'required|string',
            'billing_address.city' => 'required|string',
            'billing_address.state' => 'required|string',
            'billing_address.pincode' => 'required|string',
            'shipping_address' => 'required|array',
            'shipping_method' => 'required|string',
            'payment_method' => 'required|string',
            'notes' => 'nullable|string'
        ]);

        // Handle both authenticated and guest users
        $user = Auth::user();
        $userId = $user ? $user->id : null;
        $sessionId = session()->getId();

        // Get cart items for user or session
        $cartItems = CartItem::getCartItems($userId, $sessionId);

        if ($cartItems->isEmpty()) {
            return response()->json([
                'success' => false,
                'message' => 'Your cart is empty.'
            ], 400);
        }

        // For guest users, create a temporary user record or handle differently
        if (!$user) {
            // In development, allow guest checkout
            if (config('app.env') !== 'local') {
                return response()->json([
                    'success' => false,
                    'message' => 'Please login to complete your order.'
                ], 401);
            }

            // Create or find user by phone for guest checkout
            $phone = preg_replace('/[^0-9]/', '', $request->billing_address['phone']);
            $user = User::firstOrCreate(
                ['phone' => $phone],
                [
                    'name' => $request->billing_address['name'],
                    'email' => $request->billing_address['email'],
                    'phone_verified_at' => now(), // Auto-verify for development
                ]
            );

            // Auto-login the user
            Auth::login($user);
        }

        DB::beginTransaction();

        try {
            // Calculate totals
            $subtotal = $cartItems->sum('total_price');
            $taxRate = 0.18; // 18% GST
            $taxAmount = $subtotal * $taxRate;
            $shippingAmount = $this->calculateShipping($request->shipping_method, $subtotal);
            $discountAmount = 0; // Can be implemented later
            $totalAmount = $subtotal + $taxAmount + $shippingAmount - $discountAmount;

            // Create order
            $order = Order::create([
                'order_number' => Order::generateOrderNumber(),
                'user_id' => $user->id,
                'status' => 'pending',
                'subtotal' => $subtotal,
                'tax_amount' => $taxAmount,
                'shipping_amount' => $shippingAmount,
                'discount_amount' => $discountAmount,
                'total_amount' => $totalAmount,
                'currency' => 'INR',
                'payment_status' => 'pending',
                'payment_method' => $request->payment_method,
                'billing_address' => $request->billing_address,
                'shipping_address' => $request->shipping_address,
                'shipping_method' => $request->shipping_method,
                'notes' => $request->notes
            ]);

            // Create order items and update stock
            foreach ($cartItems as $cartItem) {
                $product = $cartItem->product;

                // Check stock availability
                if ($product->manage_stock && $product->stock_quantity < $cartItem->quantity) {
                    throw new \Exception("Insufficient stock for product: {$product->name}");
                }

                // Create order item
                OrderItem::create([
                    'order_id' => $order->id,
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'product_sku' => $product->sku,
                    'price' => $product->current_price,
                    'quantity' => $cartItem->quantity,
                    'size' => $cartItem->size,
                    'product_options' => $cartItem->options,
                    'total' => $product->current_price * $cartItem->quantity
                ]);

                // Update product stock
                $product->decrementStock($cartItem->quantity);
            }

            DB::commit();

            // Handle different payment methods
            if ($request->payment_method === 'razorpay') {
                // For Razorpay, don't clear cart yet - wait for payment confirmation
                return response()->json([
                    'success' => true,
                    'message' => 'Order created successfully!',
                    'order_id' => $order->id,
                    'payment_method' => 'razorpay',
                    'requires_payment' => true
                ]);
            } else {
                // For COD or other methods, clear cart and update user stats immediately
                CartItem::where('user_id', $user->id)->delete();
                $user->increment('total_spent', $totalAmount);
                $user->addRewardPoints(floor($totalAmount / 100)); // 1 point per ₹100
                // For COD or other methods, process immediately
                $paymentResult = $this->processPayment($order, $request->payment_method);

                if ($paymentResult['success']) {
                    $order->update([
                        'payment_status' => $request->payment_method === 'cod' ? 'pending' : 'paid',
                        'payment_transaction_id' => $paymentResult['transaction_id'],
                        'status' => 'confirmed'
                    ]);

                    // Send order confirmation SMS for COD orders
                    try {
                        if ($user->phone) {
                            $this->twilioService->sendOrderConfirmation(
                                $user->phone,
                                $order->order_number,
                                $user->name
                            );
                        }
                    } catch (\Exception $e) {
                        Log::error('Failed to send COD order confirmation SMS: ' . $e->getMessage());
                    }

                    return response()->json([
                        'success' => true,
                        'message' => 'Order placed successfully!',
                        'order_id' => $order->id,
                        'redirect_url' => route('checkout.success', $order->id)
                    ]);
                } else {
                    $order->update(['payment_status' => 'failed']);

                    return response()->json([
                        'success' => false,
                        'message' => 'Payment failed. Please try again.'
                    ], 400);
                }
            }

        } catch (\Exception $e) {
            DB::rollback();

            return response()->json([
                'success' => false,
                'message' => 'Order creation failed: ' . $e->getMessage()
            ], 500);
        }
    }

    public function success($orderId)
    {
        // Allow access to order success page without authentication for sharing
        $order = Order::with(['orderItems.product', 'user'])
            ->findOrFail($orderId);

        // If user is authenticated, verify they own the order
        if (Auth::check() && $order->user_id !== Auth::id()) {
            abort(403, 'Unauthorized access to order.');
        }

        return view('checkout.success', compact('order'));
    }



    public function cancel($id)
    {
        $order = Auth::user()->orders()->findOrFail($id);

        if (!$order->canBeCancelled()) {
            return response()->json([
                'success' => false,
                'message' => 'This order cannot be cancelled.'
            ], 400);
        }

        DB::beginTransaction();

        try {
            // Restore stock
            foreach ($order->orderItems as $orderItem) {
                $product = $orderItem->product;
                if ($product) {
                    $product->incrementStock($orderItem->quantity);
                }
            }

            // Update order status
            $order->update(['status' => 'cancelled']);

            // Update user stats
            $order->user->decrement('total_spent', $order->total_amount);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Order cancelled successfully!'
            ]);

        } catch (\Exception $e) {
            DB::rollback();

            return response()->json([
                'success' => false,
                'message' => 'Failed to cancel order: ' . $e->getMessage()
            ], 500);
        }
    }

    private function calculateShipping($method, $subtotal)
    {
        switch ($method) {
            case 'standard':
                return $subtotal >= 5000 ? 0 : 200; // Free shipping above ₹5000
            case 'express':
                return 500;
            case 'overnight':
                return 1000;
            default:
                return 200;
        }
    }

    private function processPayment($order, $paymentMethod)
    {
        // Mock payment processing
        // In real implementation, integrate with payment gateways like Razorpay, Stripe, etc.

        $success = true; // Simulate successful payment
        $transactionId = 'TXN_' . time() . '_' . $order->id;

        return [
            'success' => $success,
            'transaction_id' => $transactionId,
            'message' => $success ? 'Payment successful' : 'Payment failed'
        ];
    }
}
