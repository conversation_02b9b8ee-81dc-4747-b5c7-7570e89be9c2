<?php

namespace App\Http\Middleware;

use App\Models\CartItem;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\View;
use Symfony\Component\HttpFoundation\Response;

class CartMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Share cart count with all views
        $cartCount = $this->getCartCount();
        View::share('cartCount', $cartCount);

        return $next($request);
    }

    private function getCartCount()
    {
        $userId = Auth::id();
        $sessionId = session()->getId();

        return CartItem::getCartCount($userId, $sessionId);
    }
}
