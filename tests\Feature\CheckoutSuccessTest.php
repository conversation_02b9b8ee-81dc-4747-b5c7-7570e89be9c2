<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Order;
use App\Models\Product;
use App\Models\Category;
use App\Models\OrderItem;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class CheckoutSuccessTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $order;
    protected $product;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test user
        $this->user = User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'customer',
            'status' => 'active',
        ]);

        // Create test category
        $category = Category::create([
            'name' => 'Test Category',
            'slug' => 'test-category',
            'is_active' => true,
        ]);

        // Create test product
        $this->product = Product::create([
            'category_id' => $category->id,
            'name' => 'Test Product',
            'slug' => 'test-product',
            'sku' => 'TEST-001',
            'description' => 'Test product description',
            'price' => 100.00,
            'stock_quantity' => 10,
            'in_stock' => true,
            'status' => 'active',
            'images' => ['test.jpg'],
            'specifications' => ['test' => 'value'],
            'sizes' => ['M'],
        ]);

        // Create test order with proper address structure
        $this->order = Order::create([
            'user_id' => $this->user->id,
            'order_number' => 'TEST_' . time(),
            'subtotal' => 100.00,
            'tax_amount' => 18.00,
            'shipping_amount' => 0.00,
            'discount_amount' => 0.00,
            'total_amount' => 118.00,
            'currency' => 'INR',
            'status' => 'confirmed',
            'payment_status' => 'paid',
            'payment_method' => 'razorpay',
            'shipping_address' => [
                'address' => 'Test Address',
                'city' => 'Test City',
                'state' => 'Test State',
                'pincode' => '123456',
                'country' => 'india'
            ],
            'billing_address' => [
                'name' => 'Test User',
                'email' => '<EMAIL>',
                'phone' => '1234567890',
                'address' => 'Test Address',
                'city' => 'Test City',
                'state' => 'Test State',
                'pincode' => '123456',
                'country' => 'india'
            ]
        ]);

        // Create order item
        OrderItem::create([
            'order_id' => $this->order->id,
            'product_id' => $this->product->id,
            'product_name' => $this->product->name,
            'product_sku' => $this->product->sku,
            'quantity' => 1,
            'price' => 100.00,
            'total' => 100.00
        ]);
    }

    /** @test */
    public function it_can_display_success_page_for_valid_order()
    {
        $response = $this->actingAs($this->user)
            ->get("/checkout/success/{$this->order->id}");

        $response->assertStatus(200)
            ->assertViewIs('checkout.success')
            ->assertViewHas('order')
            ->assertSee($this->order->order_number)
            ->assertSee('Order Confirmed!')
            ->assertSee('Test Product')
            ->assertSee('₹118.00'); // Total amount
    }

    /** @test */
    public function it_displays_shipping_information_correctly()
    {
        $response = $this->actingAs($this->user)
            ->get("/checkout/success/{$this->order->id}");

        $response->assertStatus(200)
            ->assertSee('Test User') // Name from billing address
            ->assertSee('Test Address') // Shipping address
            ->assertSee('Test City, Test State') // City and state
            ->assertSee('123456') // Pincode
            ->assertSee('India'); // Country
    }

    /** @test */
    public function it_displays_order_items_correctly()
    {
        $response = $this->actingAs($this->user)
            ->get("/checkout/success/{$this->order->id}");

        $response->assertStatus(200)
            ->assertSee('Test Product')
            ->assertSee('TEST-001') // SKU
            ->assertSee('Quantity: 1')
            ->assertSee('₹100.00'); // Item price
    }

    /** @test */
    public function it_displays_order_totals_correctly()
    {
        $response = $this->actingAs($this->user)
            ->get("/checkout/success/{$this->order->id}");

        $response->assertStatus(200)
            ->assertSee('₹100.00') // Subtotal
            ->assertSee('₹18.00') // Tax
            ->assertSee('₹0.00') // Shipping
            ->assertSee('₹118.00'); // Total
    }

    /** @test */
    public function it_handles_missing_shipping_address_gracefully()
    {
        // Create order without shipping address
        $orderWithoutAddress = Order::create([
            'user_id' => $this->user->id,
            'order_number' => 'TEST_NO_ADDR_' . time(),
            'subtotal' => 50.00,
            'tax_amount' => 9.00,
            'shipping_amount' => 0.00,
            'discount_amount' => 0.00,
            'total_amount' => 59.00,
            'currency' => 'INR',
            'status' => 'confirmed',
            'payment_status' => 'paid',
            'payment_method' => 'cod',
            'shipping_address' => [],
            'billing_address' => [
                'name' => 'Test User',
                'email' => '<EMAIL>'
            ]
        ]);

        $response = $this->actingAs($this->user)
            ->get("/checkout/success/{$orderWithoutAddress->id}");

        $response->assertStatus(200)
            ->assertSee('Shipping address not available');
    }

    /** @test */
    public function it_prevents_unauthorized_access_to_other_users_orders()
    {
        // Create another user
        $otherUser = User::create([
            'name' => 'Other User',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'customer',
            'status' => 'active',
        ]);

        $response = $this->actingAs($otherUser)
            ->get("/checkout/success/{$this->order->id}");

        $response->assertStatus(404);
    }

    /** @test */
    public function it_returns_404_for_nonexistent_order()
    {
        $response = $this->actingAs($this->user)
            ->get("/checkout/success/99999");

        $response->assertStatus(404);
    }

    /** @test */
    public function it_requires_authentication()
    {
        $response = $this->get("/checkout/success/{$this->order->id}");

        $response->assertRedirect('/login');
    }

    /** @test */
    public function it_displays_payment_status_correctly()
    {
        $response = $this->actingAs($this->user)
            ->get("/checkout/success/{$this->order->id}");

        $response->assertStatus(200)
            ->assertSee('Paid') // Payment status badge
            ->assertSee('Razorpay'); // Payment method
    }

    /** @test */
    public function it_shows_action_buttons()
    {
        $response = $this->actingAs($this->user)
            ->get("/checkout/success/{$this->order->id}");

        $response->assertStatus(200)
            ->assertSee('View All Orders')
            ->assertSee('Continue Shopping');
    }
}
