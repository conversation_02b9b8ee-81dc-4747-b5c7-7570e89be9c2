<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\TwilioService;

class TestTwilioConnection extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'twilio:test {--phone= : Phone number to send test SMS}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test Twilio connection and send a test SMS';

    /**
     * Execute the console command.
     */
    public function handle(TwilioService $twilioService)
    {
        $this->info('Testing SMS Service...');

        // Check if using mock service
        $reflection = new \ReflectionClass($twilioService);
        $useMockProperty = $reflection->getProperty('useMock');
        $useMockProperty->setAccessible(true);
        $useMock = $useMockProperty->getValue($twilioService);

        if ($useMock) {
            $this->warn('⚠️  Using Mock SMS Service (Twilio not configured)');
            $this->info('This is fine for development and testing.');
        }

        // Test connection
        $connectionTest = $twilioService->testConnection();

        if ($connectionTest['success']) {
            $this->info('✅ SMS service connection successful!');
            $this->info('Account SID: ' . $connectionTest['account_sid']);
            $this->info('Account Status: ' . $connectionTest['account_status']);
        } else {
            $this->error('❌ SMS service connection failed!');
            $this->error('Error: ' . $connectionTest['error']);
            return 1;
        }

        // Test SMS sending if phone number provided
        $phone = $this->option('phone');
        if ($phone) {
            $this->info("\nSending test SMS to: " . $phone);

            $smsResult = $twilioService->sendOtp($phone, '123456', 'test');

            if ($smsResult['success']) {
                $this->info('✅ Test SMS sent successfully!');
                $this->info('Message SID: ' . $smsResult['message_sid']);

                if ($useMock) {
                    $this->info('📝 Check storage/logs/mock_sms.log for the SMS content');
                    $this->info('💡 In mock mode, the OTP is: 123456');
                }
            } else {
                $this->error('❌ Failed to send test SMS!');
                $this->error('Error: ' . ($smsResult['error'] ?? $smsResult['message']));
                return 1;
            }
        } else {
            $this->info("\nTo test SMS sending, run:");
            $this->info('php artisan twilio:test --phone=**********');
        }

        if ($useMock) {
            $this->info("\n🔧 To use real Twilio SMS:");
            $this->info('1. Get valid Twilio credentials from https://console.twilio.com/');
            $this->info('2. Update your .env file with:');
            $this->info('   TWILIO_ACCOUNT_SID=your_real_account_sid');
            $this->info('   TWILIO_AUTH_TOKEN=your_real_auth_token');
            $this->info('   TWILIO_FROM_NUMBER=your_twilio_phone_number');
        }

        $this->info("\n🎉 SMS service is ready for mobile authentication!");
        return 0;
    }
}
