@extends('layouts.admin')

@section('title', 'Admin Profile')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0">Admin Profile</h1>
            <p class="text-muted">Manage your admin account settings and preferences</p>
        </div>
        <a href="{{ route('admin.profile.edit') }}" class="btn btn-primary">
            <i class="fas fa-edit me-2"></i>Edit Profile
        </a>
    </div>

    <div class="row">
        <!-- Profile Information -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Profile Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 text-center">
                            <div class="position-relative d-inline-block">
                                @if($user->avatar)
                                <img src="{{ asset('storage/' . $user->avatar) }}"
                                     alt="{{ $user->name }}"
                                     class="rounded-circle img-fluid"
                                     style="width: 120px; height: 120px; object-fit: cover;">
                                @else
                                <div class="bg-primary-pink text-white rounded-circle d-flex align-items-center justify-content-center"
                                     style="width: 120px; height: 120px; font-size: 2rem;">
                                    {{ strtoupper(substr($user->name, 0, 2)) }}
                                </div>
                                @endif
                                <span class="position-absolute bottom-0 end-0 bg-success border border-white rounded-circle"
                                      style="width: 25px; height: 25px;" title="Online"></span>
                            </div>
                        </div>
                        <div class="col-md-9">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label class="form-label fw-bold">Full Name</label>
                                    <p class="text-muted">{{ $user->name }}</p>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label fw-bold">Email Address</label>
                                    <p class="text-muted">{{ $user->email }}</p>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label fw-bold">Phone Number</label>
                                    <p class="text-muted">{{ $user->phone ?? 'Not provided' }}</p>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label fw-bold">Role</label>
                                    <p class="text-muted">
                                        <span class="badge bg-primary">{{ ucfirst($user->role ?? 'Admin') }}</span>
                                    </p>
                                </div>
                                @if($user->bio)
                                <div class="col-12">
                                    <label class="form-label fw-bold">Bio</label>
                                    <p class="text-muted">{{ $user->bio }}</p>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Account Statistics -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Account Statistics</h5>
                </div>
                <div class="card-body">
                    <div class="row g-4">
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="h4 text-primary mb-1">{{ $stats['total_logins'] }}</div>
                                <small class="text-muted">Total Logins</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="h4 text-success mb-1">{{ $stats['account_created']?->diffInDays() ?? 0 }}</div>
                                <small class="text-muted">Days Active</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="h4 text-info mb-1">{{ $stats['last_login']?->format('M d') ?? 'Today' }}</div>
                                <small class="text-muted">Last Login</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="text-center">
                                <div class="h4 text-warning mb-1">{{ $stats['profile_updated']?->diffInDays() ?? 0 }}</div>
                                <small class="text-muted">Days Since Update</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Recent Activity</h5>
                    <a href="{{ route('admin.profile.activity') }}" class="btn btn-sm btn-outline-primary">
                        View All
                    </a>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Profile Updated</h6>
                                <p class="text-muted small mb-1">Updated profile information</p>
                                <small class="text-muted">{{ $user->updated_at?->diffForHumans() ?? 'Recently' }}</small>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-marker bg-success"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Login</h6>
                                <p class="text-muted small mb-1">Logged into admin panel</p>
                                <small class="text-muted">{{ $stats['last_login']?->diffForHumans() ?? 'Recently' }}</small>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-marker bg-info"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Account Created</h6>
                                <p class="text-muted small mb-1">Admin account was created</p>
                                <small class="text-muted">{{ $stats['account_created']?->diffForHumans() ?? 'Recently' }}</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Quick Actions -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.profile.edit') }}" class="btn btn-primary">
                            <i class="fas fa-edit me-2"></i>Edit Profile
                        </a>
                        <button type="button" class="btn btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#changePasswordModal">
                            <i class="fas fa-key me-2"></i>Change Password
                        </button>
                        <button type="button" class="btn btn-outline-info" data-bs-toggle="modal" data-bs-target="#preferencesModal">
                            <i class="fas fa-cog me-2"></i>Preferences
                        </button>
                        <a href="{{ route('admin.profile.activity') }}" class="btn btn-outline-warning">
                            <i class="fas fa-history me-2"></i>Activity Log
                        </a>
                    </div>
                </div>
            </div>

            <!-- Account Security -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">Account Security</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <i class="fas fa-shield-alt text-success me-3 fs-4"></i>
                        <div>
                            <h6 class="mb-0">Two-Factor Authentication</h6>
                            <small class="text-muted">Not enabled</small>
                        </div>
                    </div>
                    <div class="d-flex align-items-center mb-3">
                        <i class="fas fa-key text-warning me-3 fs-4"></i>
                        <div>
                            <h6 class="mb-0">Password</h6>
                            <small class="text-muted">Last changed {{ $user->updated_at?->diffForHumans() ?? 'recently' }}</small>
                        </div>
                    </div>
                    <div class="d-flex align-items-center">
                        <i class="fas fa-clock text-info me-3 fs-4"></i>
                        <div>
                            <h6 class="mb-0">Session</h6>
                            <small class="text-muted">Active since {{ $stats['last_login']?->format('M d, Y g:i A') ?? 'Today' }}</small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Information -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="mb-0">System Information</h5>
                </div>
                <div class="card-body">
                    <div class="row g-2 small">
                        <div class="col-6"><strong>User ID:</strong></div>
                        <div class="col-6 text-muted">{{ $user->id }}</div>
                        
                        <div class="col-6"><strong>Created:</strong></div>
                        <div class="col-6 text-muted">{{ $user->created_at?->format('M d, Y') ?? 'N/A' }}</div>

                        <div class="col-6"><strong>Updated:</strong></div>
                        <div class="col-6 text-muted">{{ $user->updated_at?->format('M d, Y') ?? 'N/A' }}</div>
                        
                        <div class="col-6"><strong>Timezone:</strong></div>
                        <div class="col-6 text-muted">{{ $user->preferences['timezone'] ?? 'UTC' }}</div>
                        
                        <div class="col-6"><strong>Language:</strong></div>
                        <div class="col-6 text-muted">{{ $user->preferences['language'] ?? 'English' }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Change Password Modal -->
<div class="modal fade" id="changePasswordModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="{{ route('admin.profile.password') }}">
                @csrf
                @method('PUT')
                <div class="modal-header">
                    <h5 class="modal-title">Change Password</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="current_password" class="form-label">Current Password</label>
                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">New Password</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    <div class="mb-3">
                        <label for="password_confirmation" class="form-label">Confirm New Password</label>
                        <input type="password" class="form-control" id="password_confirmation" name="password_confirmation" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Password</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Preferences Modal -->
<div class="modal fade" id="preferencesModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="POST" action="{{ route('admin.profile.preferences') }}">
                @csrf
                @method('PUT')
                <div class="modal-header">
                    <h5 class="modal-title">Admin Preferences</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label class="form-label">Notifications</label>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="email_notifications" name="email_notifications" value="1" 
                                   {{ ($user->preferences['email_notifications'] ?? true) ? 'checked' : '' }}>
                            <label class="form-check-label" for="email_notifications">Email Notifications</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="sms_notifications" name="sms_notifications" value="1"
                                   {{ ($user->preferences['sms_notifications'] ?? false) ? 'checked' : '' }}>
                            <label class="form-check-label" for="sms_notifications">SMS Notifications</label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="dashboard_layout" class="form-label">Dashboard Layout</label>
                        <select class="form-select" id="dashboard_layout" name="dashboard_layout">
                            <option value="default" {{ ($user->preferences['dashboard_layout'] ?? 'default') === 'default' ? 'selected' : '' }}>Default</option>
                            <option value="compact" {{ ($user->preferences['dashboard_layout'] ?? 'default') === 'compact' ? 'selected' : '' }}>Compact</option>
                            <option value="detailed" {{ ($user->preferences['dashboard_layout'] ?? 'default') === 'detailed' ? 'selected' : '' }}>Detailed</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="items_per_page" class="form-label">Items Per Page</label>
                        <select class="form-select" id="items_per_page" name="items_per_page">
                            <option value="15" {{ ($user->preferences['items_per_page'] ?? 15) == 15 ? 'selected' : '' }}>15</option>
                            <option value="25" {{ ($user->preferences['items_per_page'] ?? 15) == 25 ? 'selected' : '' }}>25</option>
                            <option value="50" {{ ($user->preferences['items_per_page'] ?? 15) == 50 ? 'selected' : '' }}>50</option>
                            <option value="100" {{ ($user->preferences['items_per_page'] ?? 15) == 100 ? 'selected' : '' }}>100</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="theme" class="form-label">Theme</label>
                        <select class="form-select" id="theme" name="theme">
                            <option value="light" {{ ($user->preferences['theme'] ?? 'light') === 'light' ? 'selected' : '' }}>Light</option>
                            <option value="dark" {{ ($user->preferences['theme'] ?? 'light') === 'dark' ? 'selected' : '' }}>Dark</option>
                            <option value="auto" {{ ($user->preferences['theme'] ?? 'light') === 'auto' ? 'selected' : '' }}>Auto</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Preferences</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -22px;
    top: 20px;
    width: 2px;
    height: calc(100% + 10px);
    background: #e9ecef;
}

.timeline-marker {
    position: absolute;
    left: -26px;
    top: 4px;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    border: 2px solid white;
    box-shadow: 0 0 0 2px #e9ecef;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid var(--bs-primary);
}
</style>
@endpush
