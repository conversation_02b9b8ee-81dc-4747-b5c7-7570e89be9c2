@extends('layouts.app')

@section('title', 'My Orders - ShreeJi Jewelry')
@section('description', 'View and track all your ShreeJi jewelry orders.')

@section('content')
<!-- Page Header -->
<section class="py-4 bg-light">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h1 class="font-playfair display-6 mb-2">My Orders</h1>
                <p class="text-muted mb-0">Track and manage your jewelry orders</p>
            </div>
            <div class="col-md-6 text-md-end">
                <a href="{{ route('collections') }}" class="btn btn-primary-pink">
                    <i class="fas fa-plus me-2"></i>Shop More
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Orders Content -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <!-- Filters -->
            <div class="col-lg-3 mb-4">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-light">
                        <h6 class="mb-0">Filter Orders</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label class="form-label">Order Status</label>
                            <select class="form-select">
                                <option value="">All Orders</option>
                                <option value="pending">Pending</option>
                                <option value="processing">Processing</option>
                                <option value="shipped">Shipped</option>
                                <option value="delivered">Delivered</option>
                                <option value="cancelled">Cancelled</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Date Range</label>
                            <select class="form-select">
                                <option value="">All Time</option>
                                <option value="30">Last 30 Days</option>
                                <option value="90">Last 3 Months</option>
                                <option value="365">Last Year</option>
                            </select>
                        </div>
                        <button class="btn btn-outline-primary w-100">Apply Filters</button>
                    </div>
                </div>
            </div>
            
            <!-- Orders List -->
            <div class="col-lg-9">
                @if($orders->count() > 0)
                    @foreach($orders as $order)
                    <div class="card border-0 shadow-sm mb-4">
                        <div class="card-header bg-light d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="mb-0">Order #{{ $order->order_number }}</h6>
                                <small class="text-muted">Placed on {{ $order->created_at->format('F d, Y') }}</small>
                            </div>
                            <span class="badge {{ $order->getStatusBadgeClass() }}">{{ $order->getStatusDisplayName() }}</span>
                        </div>
                        <div class="card-body">
                            @foreach($order->orderItems as $item)
                            <div class="row g-3 {{ !$loop->last ? 'border-bottom pb-3 mb-3' : '' }}">
                                <div class="col-md-3">
                                    @if($item->product && $item->product->images && count($item->product->images) > 0)
                                        <img src="{{ $item->product->images[0] }}"
                                             alt="{{ $item->product_name }}" class="img-fluid rounded">
                                    @else
                                        <div class="bg-light d-flex align-items-center justify-content-center rounded"
                                             style="height: 120px;">
                                            <i class="fas fa-gem text-muted fs-2"></i>
                                        </div>
                                    @endif
                                </div>
                                <div class="col-md-6">
                                    <h6 class="mb-1">{{ $item->product_name }}</h6>
                                    <p class="text-muted mb-1">SKU: {{ $item->product_sku }}</p>
                                    @if($item->size)
                                        <p class="text-muted mb-1">Size: {{ $item->size }}</p>
                                    @endif
                                    <p class="text-muted mb-2">Quantity: {{ $item->quantity }}</p>
                                </div>
                                <div class="col-md-3 text-md-end">
                                    <h6 class="text-primary-pink mb-1">₹{{ number_format($item->total, 2) }}</h6>
                                    <small class="text-muted">₹{{ number_format($item->price, 2) }} each</small>
                                </div>
                            </div>
                            @endforeach

                            <div class="row mt-3">
                                <div class="col-md-6">
                                    <div class="d-flex gap-2">
                                        <a href="{{ route('order.detail', $order->id) }}" class="btn btn-outline-primary btn-sm">View Details</a>
                                        @if($order->status === 'delivered')
                                            <button class="btn btn-outline-secondary btn-sm">Download Invoice</button>
                                            <button class="btn btn-outline-success btn-sm">Buy Again</button>
                                        @elseif(in_array($order->status, ['shipped', 'out_for_delivery']))
                                            <button class="btn btn-primary-pink btn-sm">Track Package</button>
                                        @endif
                                        @if($order->canBeCancelled())
                                            <button class="btn btn-outline-danger btn-sm" onclick="cancelOrder({{ $order->id }})">Cancel Order</button>
                                        @endif
                                    </div>
                                </div>
                                <div class="col-md-6 text-md-end">
                                    <h5 class="text-primary-pink mb-1">₹{{ number_format($order->total_amount, 2) }}</h5>
                                    <p class="text-muted mb-1">
                                        @if($order->status === 'delivered')
                                            Delivered on {{ $order->delivered_at ? $order->delivered_at->format('M d, Y') : 'N/A' }}
                                        @elseif($order->status === 'shipped')
                                            Expected delivery in 2-3 days
                                        @elseif($order->status === 'processing')
                                            Being prepared
                                        @else
                                            {{ ucfirst($order->status) }}
                                        @endif
                                    </p>
                                    @if(in_array($order->status, ['processing', 'shipped', 'out_for_delivery']))
                                        <div class="progress mb-2" style="height: 6px;">
                                            @php
                                                $progress = match($order->status) {
                                                    'processing' => 25,
                                                    'shipped' => 75,
                                                    'out_for_delivery' => 90,
                                                    default => 10
                                                };
                                            @endphp
                                            <div class="progress-bar" style="width: {{ $progress }}%"></div>
                                        </div>
                                        <small class="text-muted">{{ ucfirst($order->status) }}</small>
                                    @endif
                                    @if($order->status === 'delivered')
                                        <button class="btn btn-outline-warning btn-sm">Rate & Review</button>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                @else
                    <div class="card border-0 shadow-sm">
                        <div class="card-body text-center py-5">
                            <i class="fas fa-shopping-bag text-muted fs-1 mb-3"></i>
                            <h5 class="text-muted mb-3">No Orders Found</h5>
                            <p class="text-muted mb-4">You haven't placed any orders yet. Start shopping to see your orders here.</p>
                            <a href="{{ route('collections') }}" class="btn btn-primary-pink">
                                <i class="fas fa-shopping-bag me-2"></i>Start Shopping
                            </a>
                        </div>
                    </div>
                @endif

                <!-- Pagination -->
                @if($orders->hasPages())
                    <div class="d-flex justify-content-center">
                        {{ $orders->links() }}
                    </div>
                @endif
            </div>
        </div>
    </div>
</section>

<!-- Order Summary Stats -->
<section class="py-5 bg-light">
    <div class="container">
        <h3 class="font-playfair mb-4 text-center">Order Statistics</h3>
        <div class="row g-4">
            <div class="col-lg-3 col-md-6">
                <div class="card border-0 shadow-sm text-center">
                    <div class="card-body">
                        <i class="fas fa-shopping-bag text-primary-pink fs-2 mb-3"></i>
                        <h4 class="font-playfair text-primary-pink">{{ auth()->user()->orders()->count() }}</h4>
                        <p class="text-muted mb-0">Total Orders</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card border-0 shadow-sm text-center">
                    <div class="card-body">
                        <i class="fas fa-rupee-sign text-success fs-2 mb-3"></i>
                        <h4 class="font-playfair text-success">₹{{ number_format(auth()->user()->total_spent ?? 0, 0) }}</h4>
                        <p class="text-muted mb-0">Total Spent</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card border-0 shadow-sm text-center">
                    <div class="card-body">
                        <i class="fas fa-star text-warning fs-2 mb-3"></i>
                        <h4 class="font-playfair text-warning">{{ auth()->user()->reward_points ?? 0 }}</h4>
                        <p class="text-muted mb-0">Reward Points</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="card border-0 shadow-sm text-center">
                    <div class="card-body">
                        <i class="fas fa-crown text-primary-pink fs-2 mb-3"></i>
                        <h4 class="font-playfair text-primary-pink">
                            @php
                                $totalSpent = auth()->user()->total_spent ?? 0;
                                $memberStatus = match(true) {
                                    $totalSpent >= 500000 => 'VIP',
                                    $totalSpent >= 100000 => 'Gold',
                                    $totalSpent >= 50000 => 'Silver',
                                    default => 'Bronze'
                                };
                            @endphp
                            {{ $memberStatus }}
                        </h4>
                        <p class="text-muted mb-0">Member Status</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('styles')
<style>
    .progress-bar {
        background-color: var(--primary-brown) !important;
    }

    .pagination .page-link {
        color: var(--primary-brown);
        border-color: var(--primary-brown);
    }

    .pagination .page-item.active .page-link {
        background-color: var(--primary-brown);
        border-color: var(--primary-brown);
    }

    .pagination .page-link:hover {
        background-color: var(--secondary-brown);
        border-color: var(--secondary-brown);
        color: white;
    }
</style>
@endpush

@push('scripts')
<script>
function cancelOrder(orderId) {
    if (confirm('Are you sure you want to cancel this order? This action cannot be undone.')) {
        fetch(`/orders/${orderId}/cancel`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Order cancelled successfully!');
                location.reload();
            } else {
                alert(data.message || 'Failed to cancel order. Please try again.');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred. Please try again.');
        });
    }
}

// Filter functionality
document.addEventListener('DOMContentLoaded', function() {
    const statusFilter = document.querySelector('select[name="status"]');
    const dateFilter = document.querySelector('select[name="date"]');
    const applyButton = document.querySelector('.btn-outline-primary');

    if (applyButton) {
        applyButton.addEventListener('click', function() {
            const status = statusFilter ? statusFilter.value : '';
            const date = dateFilter ? dateFilter.value : '';

            let url = new URL(window.location.href);

            if (status) {
                url.searchParams.set('status', status);
            } else {
                url.searchParams.delete('status');
            }

            if (date) {
                url.searchParams.set('date', date);
            } else {
                url.searchParams.delete('date');
            }

            window.location.href = url.toString();
        });
    }
});
</script>
@endpush
