<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Product;
use App\Models\Category;

class AdditionalProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = Category::all()->keyBy('slug');

        $additionalProducts = [
            // More Rings
            [
                'category_id' => $categories['rings']->id,
                'name' => 'Classic Solitaire Diamond Ring',
                'slug' => 'classic-solitaire-diamond-ring',
                'sku' => 'RING-SOL-001',
                'description' => 'Timeless solitaire diamond ring featuring a brilliant cut diamond in a classic 6-prong setting. Perfect for engagements and special occasions.',
                'short_description' => 'Classic solitaire diamond ring with brilliant cut diamond',
                'price' => 45000.00,
                'sale_price' => 42000.00,
                'stock_quantity' => 8,
                'manage_stock' => true,
                'in_stock' => true,
                'is_featured' => true,
                'status' => 'active',
                'images' => [
                    'https://images.unsplash.com/photo-1605100804763-247f67b3557e?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80',
                    'https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
                ],
                'specifications' => [
                    'Metal' => '18K White Gold',
                    'Diamond Cut' => 'Round Brilliant',
                    'Diamond Weight' => '1.0 Carat',
                    'Clarity' => 'VS1',
                    'Color' => 'F'
                ],
                'sizes' => ['5', '6', '7', '8', '9'],
                'weight' => 3.5,
                'metal_type' => '18K White Gold',
                'metal_purity' => '18K',
                'stone_type' => 'Diamond',
                'stone_weight' => 1.0,
                'certification' => 'GIA',
                'sort_order' => 6,
            ],
            [
                'category_id' => $categories['rings']->id,
                'name' => 'Vintage Rose Gold Band',
                'slug' => 'vintage-rose-gold-band',
                'sku' => 'RING-VIN-002',
                'description' => 'Elegant vintage-inspired rose gold band with intricate milgrain detailing and small diamonds.',
                'short_description' => 'Vintage rose gold band with milgrain detailing',
                'price' => 15000.00,
                'sale_price' => null,
                'stock_quantity' => 12,
                'manage_stock' => true,
                'in_stock' => true,
                'is_featured' => false,
                'status' => 'active',
                'images' => [
                    'https://images.unsplash.com/photo-1605100804763-247f67b3557e?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
                ],
                'specifications' => [
                    'Metal' => '14K Rose Gold',
                    'Style' => 'Vintage',
                    'Width' => '3mm'
                ],
                'sizes' => ['5', '6', '7', '8', '9', '10'],
                'weight' => 2.8,
                'metal_type' => '14K Rose Gold',
                'metal_purity' => '14K',
                'stone_type' => 'Diamond',
                'stone_weight' => 0.15,
                'certification' => null,
                'sort_order' => 7,
            ],

            // More Necklaces
            [
                'category_id' => $categories['necklaces']->id,
                'name' => 'Pearl Strand Necklace',
                'slug' => 'pearl-strand-necklace',
                'sku' => 'NECK-PEARL-001',
                'description' => 'Elegant freshwater pearl strand necklace with lustrous white pearls and sterling silver clasp.',
                'short_description' => 'Freshwater pearl strand necklace',
                'price' => 8500.00,
                'sale_price' => 7500.00,
                'stock_quantity' => 15,
                'manage_stock' => true,
                'in_stock' => true,
                'is_featured' => true,
                'status' => 'active',
                'images' => [
                    'https://images.unsplash.com/photo-1599643478518-a784e5dc4c8f?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
                ],
                'specifications' => [
                    'Pearl Type' => 'Freshwater',
                    'Pearl Size' => '7-8mm',
                    'Length' => '18 inches',
                    'Clasp' => 'Sterling Silver'
                ],
                'sizes' => ['16"', '18"', '20"'],
                'weight' => 25.0,
                'metal_type' => 'Sterling Silver',
                'metal_purity' => '925',
                'stone_type' => 'Pearl',
                'stone_weight' => null,
                'certification' => null,
                'sort_order' => 8,
            ],
            [
                'category_id' => $categories['necklaces']->id,
                'name' => 'Gold Chain Pendant Necklace',
                'slug' => 'gold-chain-pendant-necklace',
                'sku' => 'NECK-GOLD-002',
                'description' => 'Delicate 22K gold chain necklace with traditional Indian pendant design.',
                'short_description' => '22K gold chain with traditional pendant',
                'price' => 35000.00,
                'sale_price' => null,
                'stock_quantity' => 6,
                'manage_stock' => true,
                'in_stock' => true,
                'is_featured' => false,
                'status' => 'active',
                'images' => [
                    'https://images.unsplash.com/photo-1599643478518-a784e5dc4c8f?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
                ],
                'specifications' => [
                    'Metal' => '22K Gold',
                    'Chain Type' => 'Box Chain',
                    'Pendant Style' => 'Traditional Indian'
                ],
                'sizes' => ['16"', '18"', '20"', '22"'],
                'weight' => 12.5,
                'metal_type' => '22K Gold',
                'metal_purity' => '22K',
                'stone_type' => null,
                'stone_weight' => null,
                'certification' => 'BIS Hallmark',
                'sort_order' => 9,
            ],

            // More Earrings
            [
                'category_id' => $categories['earrings']->id,
                'name' => 'Diamond Stud Earrings',
                'slug' => 'diamond-stud-earrings',
                'sku' => 'EAR-STUD-001',
                'description' => 'Classic diamond stud earrings with round brilliant cut diamonds in white gold settings.',
                'short_description' => 'Classic diamond stud earrings',
                'price' => 25000.00,
                'sale_price' => 22000.00,
                'stock_quantity' => 10,
                'manage_stock' => true,
                'in_stock' => true,
                'is_featured' => true,
                'status' => 'active',
                'images' => [
                    'https://images.unsplash.com/photo-1535632066927-ab7c9ab60908?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
                ],
                'specifications' => [
                    'Metal' => '14K White Gold',
                    'Diamond Cut' => 'Round Brilliant',
                    'Total Carat Weight' => '1.0 ct',
                    'Backing' => 'Push Back'
                ],
                'sizes' => ['One Size'],
                'weight' => 1.8,
                'metal_type' => '14K White Gold',
                'metal_purity' => '14K',
                'stone_type' => 'Diamond',
                'stone_weight' => 1.0,
                'certification' => 'GIA',
                'sort_order' => 10,
            ],
            [
                'category_id' => $categories['earrings']->id,
                'name' => 'Chandelier Drop Earrings',
                'slug' => 'chandelier-drop-earrings',
                'sku' => 'EAR-CHAN-002',
                'description' => 'Stunning chandelier drop earrings with multiple tiers of crystals and gold plating.',
                'short_description' => 'Chandelier drop earrings with crystals',
                'price' => 4500.00,
                'sale_price' => null,
                'stock_quantity' => 20,
                'manage_stock' => true,
                'in_stock' => true,
                'is_featured' => false,
                'status' => 'active',
                'images' => [
                    'https://images.unsplash.com/photo-1535632066927-ab7c9ab60908?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
                ],
                'specifications' => [
                    'Metal' => 'Gold Plated',
                    'Stone' => 'Crystal',
                    'Length' => '3 inches',
                    'Style' => 'Chandelier'
                ],
                'sizes' => ['One Size'],
                'weight' => 8.2,
                'metal_type' => 'Gold Plated',
                'metal_purity' => 'Gold Plated',
                'stone_type' => 'Crystal',
                'stone_weight' => null,
                'certification' => null,
                'sort_order' => 11,
            ],

            // More Bracelets
            [
                'category_id' => $categories['bracelets']->id,
                'name' => 'Tennis Bracelet',
                'slug' => 'tennis-bracelet',
                'sku' => 'BRAC-TEN-001',
                'description' => 'Classic tennis bracelet with a continuous line of brilliant cut diamonds in white gold.',
                'short_description' => 'Diamond tennis bracelet',
                'price' => 55000.00,
                'sale_price' => 50000.00,
                'stock_quantity' => 4,
                'manage_stock' => true,
                'in_stock' => true,
                'is_featured' => true,
                'status' => 'active',
                'images' => [
                    'https://images.unsplash.com/photo-1611591437281-460bfbe1220a?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
                ],
                'specifications' => [
                    'Metal' => '18K White Gold',
                    'Total Diamond Weight' => '3.0 ct',
                    'Number of Diamonds' => '50',
                    'Length' => '7 inches'
                ],
                'sizes' => ['6.5"', '7"', '7.5"', '8"'],
                'weight' => 15.8,
                'metal_type' => '18K White Gold',
                'metal_purity' => '18K',
                'stone_type' => 'Diamond',
                'stone_weight' => 3.0,
                'certification' => 'GIA',
                'sort_order' => 12,
            ],

            // Jewelry Sets
            [
                'category_id' => $categories['sets']->id,
                'name' => 'Bridal Jewelry Set',
                'slug' => 'bridal-jewelry-set',
                'sku' => 'SET-BRID-001',
                'description' => 'Complete bridal jewelry set including necklace, earrings, and bracelet with matching design.',
                'short_description' => 'Complete bridal jewelry set',
                'price' => 85000.00,
                'sale_price' => 75000.00,
                'stock_quantity' => 3,
                'manage_stock' => true,
                'in_stock' => true,
                'is_featured' => true,
                'status' => 'active',
                'images' => [
                    'https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80'
                ],
                'specifications' => [
                    'Metal' => '22K Gold',
                    'Set Includes' => 'Necklace, Earrings, Bracelet',
                    'Style' => 'Traditional Indian',
                    'Stone' => 'Kundan and Pearls'
                ],
                'sizes' => ['One Size'],
                'weight' => 45.0,
                'metal_type' => '22K Gold',
                'metal_purity' => '22K',
                'stone_type' => 'Kundan',
                'stone_weight' => null,
                'certification' => 'BIS Hallmark',
                'sort_order' => 13,
            ]
        ];

        foreach ($additionalProducts as $product) {
            Product::create($product);
        }
    }
}
