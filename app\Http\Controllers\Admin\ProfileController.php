<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\RedirectResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rules\Password;

class ProfileController extends Controller
{
    /**
     * Show the admin profile page.
     */
    public function show(): View
    {
        $user = auth()->user();

        // Ensure user has required fields
        if (!$user->created_at) {
            $user->created_at = now();
        }
        if (!$user->updated_at) {
            $user->updated_at = now();
        }
        if (!$user->preferences) {
            $user->preferences = [];
        }

        // Get admin statistics
        $stats = [
            'total_logins' => $user->login_count ?? 0,
            'last_login' => $user->last_login_at,
            'account_created' => $user->created_at ?? now(),
            'profile_updated' => $user->updated_at ?? now(),
        ];

        return view('admin.profile.show', compact('user', 'stats'));
    }

    /**
     * Show the edit profile form.
     */
    public function edit(): View
    {
        $user = auth()->user();

        // Ensure user has required fields
        if (!$user->created_at) {
            $user->created_at = now();
        }
        if (!$user->updated_at) {
            $user->updated_at = now();
        }
        if (!$user->preferences) {
            $user->preferences = [];
        }

        return view('admin.profile.edit', compact('user'));
    }

    /**
     * Update the admin profile.
     */
    public function update(Request $request): RedirectResponse
    {
        $user = auth()->user();

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'phone' => 'nullable|string|max:20',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'bio' => 'nullable|string|max:500',
            'timezone' => 'nullable|string|max:50',
            'language' => 'nullable|string|max:10',
        ]);

        // Handle avatar upload
        if ($request->hasFile('avatar')) {
            // Delete old avatar if exists
            if ($user->avatar && Storage::disk('public')->exists($user->avatar)) {
                Storage::disk('public')->delete($user->avatar);
            }

            $avatarPath = $request->file('avatar')->store('avatars', 'public');
            $validated['avatar'] = $avatarPath;
        }

        $user->update($validated);

        return redirect()
            ->route('admin.profile.show')
            ->with('success', 'Profile updated successfully.');
    }

    /**
     * Update the admin password.
     */
    public function updatePassword(Request $request): RedirectResponse
    {
        $user = auth()->user();

        $validated = $request->validate([
            'current_password' => 'required|current_password',
            'password' => ['required', 'confirmed', Password::defaults()],
        ]);

        $user->update([
            'password' => Hash::make($validated['password']),
        ]);

        return redirect()
            ->route('admin.profile.show')
            ->with('success', 'Password updated successfully.');
    }

    /**
     * Update admin preferences.
     */
    public function updatePreferences(Request $request): RedirectResponse
    {
        $user = auth()->user();

        $validated = $request->validate([
            'email_notifications' => 'boolean',
            'sms_notifications' => 'boolean',
            'dashboard_layout' => 'nullable|string|in:default,compact,detailed',
            'items_per_page' => 'nullable|integer|min:10|max:100',
            'theme' => 'nullable|string|in:light,dark,auto',
        ]);

        // Store preferences in user's preferences column (JSON)
        $preferences = $user->preferences ?? [];
        $preferences = array_merge($preferences, $validated);

        $user->update(['preferences' => $preferences]);

        return redirect()
            ->route('admin.profile.show')
            ->with('success', 'Preferences updated successfully.');
    }

    /**
     * Show admin activity log.
     */
    public function activity(): View
    {
        $user = auth()->user();

        // You can implement activity logging later
        $activities = collect([
            [
                'action' => 'Profile Updated',
                'description' => 'Updated profile information',
                'timestamp' => $user->updated_at ?? now(),
                'ip_address' => request()->ip(),
            ],
            [
                'action' => 'Login',
                'description' => 'Logged into admin panel',
                'timestamp' => $user->last_login_at ?? now(),
                'ip_address' => request()->ip(),
            ],
        ]);

        return view('admin.profile.activity', compact('user', 'activities'));
    }
}
