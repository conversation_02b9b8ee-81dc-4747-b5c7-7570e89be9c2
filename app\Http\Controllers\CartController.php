<?php

namespace App\Http\Controllers;

use App\Models\CartItem;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;

class CartController extends Controller
{
    public function index()
    {
        $cartItems = $this->getCartItems();
        $cartCount = $this->getCartCount();

        // Calculate detailed totals
        $subtotal = 0;
        $discount = 0;

        foreach ($cartItems as $item) {
            $itemPrice = $item->product->isOnSale() ? $item->product->sale_price : $item->product->price;
            $itemTotal = $itemPrice * $item->quantity;
            $subtotal += $itemTotal;

            // Calculate discount if product is on sale
            if ($item->product->isOnSale()) {
                $originalTotal = $item->product->price * $item->quantity;
                $discount += ($originalTotal - $itemTotal);
            }
        }

        // Calculate shipping
        $shipping = $subtotal >= 25000 ? 0 : 500;

        // Calculate tax (3% GST)
        $tax = ($subtotal - $discount) * 0.03;

        // Calculate total
        $total = $subtotal - $discount + $shipping + $tax;

        return view('cart.index', compact('cartItems', 'cartCount', 'subtotal', 'discount', 'shipping', 'tax', 'total'));
    }

    public function add(Request $request)
    {
        $request->validate([
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|integer|min:1',
            'size' => 'nullable|string',
            'options' => 'nullable|array'
        ]);

        $product = Product::findOrFail($request->product_id);

        // Check if product is in stock
        if (!$product->in_stock || ($product->manage_stock && $product->stock_quantity < $request->quantity)) {
            return response()->json([
                'success' => false,
                'message' => 'Product is out of stock or insufficient quantity available.'
            ], 400);
        }

        $userId = Auth::id();
        $sessionId = Session::getId();

        // Check if item already exists in cart
        $existingItem = CartItem::where('product_id', $request->product_id)
            ->where(function($query) use ($userId, $sessionId) {
                if ($userId) {
                    $query->where('user_id', $userId);
                } else {
                    $query->where('session_id', $sessionId);
                }
            })
            ->where('size', $request->size)
            ->first();

        if ($existingItem) {
            $newQuantity = $existingItem->quantity + $request->quantity;

            // Check stock again for new quantity
            if ($product->manage_stock && $product->stock_quantity < $newQuantity) {
                return response()->json([
                    'success' => false,
                    'message' => 'Cannot add more items. Insufficient stock available.'
                ], 400);
            }

            $existingItem->update(['quantity' => $newQuantity]);
        } else {
            CartItem::create([
                'user_id' => $userId,
                'session_id' => $userId ? null : $sessionId,
                'product_id' => $request->product_id,
                'quantity' => $request->quantity,
                'size' => $request->size,
                'options' => $request->options
            ]);
        }

        $cartCount = $this->getCartCount();

        return response()->json([
            'success' => true,
            'message' => 'Product added to cart successfully!',
            'cart_count' => $cartCount
        ]);
    }

    public function update(Request $request)
    {
        $request->validate([
            'item_id' => 'required|integer',
            'quantity' => 'required|integer|min:1'
        ]);

        $cartItem = $this->findCartItem($request->item_id);

        if (!$cartItem) {
            return response()->json([
                'success' => false,
                'message' => 'Cart item not found.'
            ], 404);
        }

        $product = $cartItem->product;

        // Check stock
        if ($product->manage_stock && $product->stock_quantity < $request->quantity) {
            return response()->json([
                'success' => false,
                'message' => 'Insufficient stock available.'
            ], 400);
        }

        $cartItem->update(['quantity' => $request->quantity]);

        // Recalculate totals
        $totals = $this->calculateTotals();

        return response()->json([
            'success' => true,
            'message' => 'Cart updated successfully!',
            'cart_count' => $this->getCartCount(),
            'subtotal' => $totals['subtotal'],
            'discount' => $totals['discount'],
            'tax' => $totals['tax'],
            'total' => $totals['total']
        ]);
    }

    public function remove(Request $request)
    {
        $request->validate([
            'item_id' => 'required|integer'
        ]);

        $cartItem = $this->findCartItem($request->item_id);

        if (!$cartItem) {
            return response()->json([
                'success' => false,
                'message' => 'Cart item not found.'
            ], 404);
        }

        $cartItem->delete();

        // Recalculate totals
        $totals = $this->calculateTotals();

        return response()->json([
            'success' => true,
            'message' => 'Item removed from cart!',
            'cart_count' => $this->getCartCount(),
            'subtotal' => $totals['subtotal'],
            'discount' => $totals['discount'],
            'tax' => $totals['tax'],
            'total' => $totals['total']
        ]);
    }

    public function clear()
    {
        $userId = Auth::id();
        $sessionId = Session::getId();

        $query = CartItem::query();

        if ($userId) {
            $query->where('user_id', $userId);
        } else {
            $query->where('session_id', $sessionId);
        }

        $query->delete();

        return response()->json([
            'success' => true,
            'message' => 'Cart cleared successfully!'
        ]);
    }

    public function count()
    {
        return response()->json([
            'count' => $this->getCartCount()
        ]);
    }

    private function getCartItems()
    {
        $userId = Auth::id();
        $sessionId = Session::getId();

        return CartItem::getCartItems($userId, $sessionId);
    }

    private function getCartTotal()
    {
        $userId = Auth::id();
        $sessionId = Session::getId();

        return CartItem::getCartTotal($userId, $sessionId);
    }

    private function getCartCount()
    {
        $userId = Auth::id();
        $sessionId = Session::getId();

        return CartItem::getCartCount($userId, $sessionId);
    }

    private function findCartItem($id)
    {
        $userId = Auth::id();
        $sessionId = Session::getId();

        $query = CartItem::where('id', $id);

        if ($userId) {
            $query->where('user_id', $userId);
        } else {
            $query->where('session_id', $sessionId);
        }

        return $query->first();
    }

    private function calculateTotals()
    {
        $cartItems = $this->getCartItems();

        $subtotal = 0;
        $discount = 0;

        foreach ($cartItems as $item) {
            $itemPrice = $item->product->isOnSale() ? $item->product->sale_price : $item->product->price;
            $itemTotal = $itemPrice * $item->quantity;
            $subtotal += $itemTotal;

            // Calculate discount if product is on sale
            if ($item->product->isOnSale()) {
                $originalTotal = $item->product->price * $item->quantity;
                $discount += ($originalTotal - $itemTotal);
            }
        }

        // Calculate shipping
        $shipping = $subtotal >= 25000 ? 0 : 500;

        // Calculate tax (3% GST)
        $tax = ($subtotal - $discount) * 0.03;

        // Calculate total
        $total = $subtotal - $discount + $shipping + $tax;

        return [
            'subtotal' => $subtotal,
            'discount' => $discount,
            'shipping' => $shipping,
            'tax' => $tax,
            'total' => $total
        ];
    }

    public function moveToWishlist(Request $request)
    {
        $request->validate([
            'item_id' => 'required|integer'
        ]);

        $cartItem = $this->findCartItem($request->item_id);

        if (!$cartItem) {
            return response()->json([
                'success' => false,
                'message' => 'Cart item not found.'
            ], 404);
        }

        if (!Auth::check()) {
            return response()->json([
                'success' => false,
                'message' => 'Please login to use wishlist.'
            ], 401);
        }

        // Check if item already in wishlist
        $existingWishlist = \App\Models\Wishlist::where('user_id', Auth::id())
            ->where('product_id', $cartItem->product_id)
            ->first();

        if (!$existingWishlist) {
            \App\Models\Wishlist::create([
                'user_id' => Auth::id(),
                'product_id' => $cartItem->product_id
            ]);
        }

        $cartItem->delete();

        // Recalculate totals
        $totals = $this->calculateTotals();

        return response()->json([
            'success' => true,
            'message' => 'Item moved to wishlist!',
            'cart_count' => $this->getCartCount(),
            'subtotal' => $totals['subtotal'],
            'discount' => $totals['discount'],
            'tax' => $totals['tax'],
            'total' => $totals['total']
        ]);
    }
}
