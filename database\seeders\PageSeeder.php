<?php

namespace Database\Seeders;

use App\Models\Page;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class PageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $pages = [
            [
                'title' => 'About Us',
                'slug' => 'about',
                'content' => $this->getAboutContent(),
                'excerpt' => 'Discover the story behind ShreeJi Jewelry. Learn about our heritage, craftsmanship, and commitment to creating exquisite jewelry pieces.',
                'meta_title' => 'About Us - ShreeJi Jewelry',
                'meta_description' => 'Discover the story behind ShreeJi Jewelry. Learn about our heritage, craftsmanship, and commitment to creating exquisite jewelry pieces.',
                'meta_keywords' => ['about', 'jewelry', 'heritage', 'craftsmanship', 'shreejimarg'],
                'template' => 'about',
                'is_published' => true,
                'show_in_menu' => true,
                'menu_order' => 1,
                'published_at' => now(),
            ],
            [
                'title' => 'Contact Us',
                'slug' => 'contact',
                'content' => $this->getContactContent(),
                'excerpt' => 'Get in touch with ShreeJi Jewelry. Visit our store, call us, or send us a message for any inquiries about our jewelry collection.',
                'meta_title' => 'Contact Us - ShreeJi Jewelry',
                'meta_description' => 'Get in touch with ShreeJi Jewelry. Visit our store, call us, or send us a message for any inquiries about our jewelry collection.',
                'meta_keywords' => ['contact', 'jewelry store', 'customer service', 'shreejimarg'],
                'template' => 'contact',
                'is_published' => true,
                'show_in_menu' => true,
                'menu_order' => 2,
                'published_at' => now(),
            ],
            [
                'title' => 'Shipping Information',
                'slug' => 'shipping',
                'content' => $this->getShippingContent(),
                'excerpt' => 'Learn about our shipping policies, delivery times, and shipping costs for jewelry orders at ShreeJi.',
                'meta_title' => 'Shipping Information - ShreeJi Jewelry',
                'meta_description' => 'Learn about our shipping policies, delivery times, and shipping costs for jewelry orders at ShreeJi.',
                'meta_keywords' => ['shipping', 'delivery', 'jewelry shipping', 'policies'],
                'template' => 'guide',
                'is_published' => true,
                'show_in_menu' => false,
                'menu_order' => 10,
                'published_at' => now(),
            ],
            [
                'title' => 'Returns & Exchange Policy',
                'slug' => 'returns',
                'content' => $this->getReturnsContent(),
                'excerpt' => 'Learn about our hassle-free returns and exchange policy for jewelry purchases at ShreeJi.',
                'meta_title' => 'Returns & Exchange Policy - ShreeJi Jewelry',
                'meta_description' => 'Learn about our hassle-free returns and exchange policy for jewelry purchases at ShreeJi.',
                'meta_keywords' => ['returns', 'exchange', 'policy', 'jewelry returns'],
                'template' => 'policy',
                'is_published' => true,
                'show_in_menu' => false,
                'menu_order' => 11,
                'published_at' => now(),
            ],
            [
                'title' => 'Size Guide',
                'slug' => 'size-guide',
                'content' => $this->getSizeGuideContent(),
                'excerpt' => 'Find the perfect fit with our comprehensive jewelry size guide for rings, bangles, necklaces, and more.',
                'meta_title' => 'Size Guide - ShreeJi Jewelry',
                'meta_description' => 'Find the perfect fit with our comprehensive jewelry size guide for rings, bangles, necklaces, and more.',
                'meta_keywords' => ['size guide', 'ring size', 'jewelry sizing', 'measurements'],
                'template' => 'guide',
                'is_published' => true,
                'show_in_menu' => false,
                'menu_order' => 12,
                'published_at' => now(),
            ],
            [
                'title' => 'Jewelry Care Guide',
                'slug' => 'jewelry-care',
                'content' => $this->getJewelryCareContent(),
                'excerpt' => 'Learn how to properly care for your precious jewelry with our comprehensive care guide and maintenance tips.',
                'meta_title' => 'Jewelry Care Guide - ShreeJi Jewelry',
                'meta_description' => 'Learn how to properly care for your precious jewelry with our comprehensive care guide and maintenance tips.',
                'meta_keywords' => ['jewelry care', 'maintenance', 'cleaning', 'storage'],
                'template' => 'guide',
                'is_published' => true,
                'show_in_menu' => false,
                'menu_order' => 13,
                'published_at' => now(),
            ],
            [
                'title' => 'Warranty Information',
                'slug' => 'warranty',
                'content' => $this->getWarrantyContent(),
                'excerpt' => 'Learn about our comprehensive warranty coverage, terms, and conditions for all ShreeJi jewelry purchases.',
                'meta_title' => 'Warranty Information - ShreeJi Jewelry',
                'meta_description' => 'Learn about our comprehensive warranty coverage, terms, and conditions for all ShreeJi jewelry purchases.',
                'meta_keywords' => ['warranty', 'guarantee', 'jewelry warranty', 'coverage'],
                'template' => 'policy',
                'is_published' => true,
                'show_in_menu' => false,
                'menu_order' => 14,
                'published_at' => now(),
            ],
            [
                'title' => 'Privacy Policy',
                'slug' => 'privacy-policy',
                'content' => $this->getPrivacyPolicyContent(),
                'excerpt' => 'Read our privacy policy to understand how we collect, use, and protect your personal information at ShreeJi Jewelry.',
                'meta_title' => 'Privacy Policy - ShreeJi Jewelry',
                'meta_description' => 'Read our privacy policy to understand how we collect, use, and protect your personal information at ShreeJi Jewelry.',
                'meta_keywords' => ['privacy policy', 'data protection', 'personal information'],
                'template' => 'legal',
                'is_published' => true,
                'show_in_menu' => false,
                'menu_order' => 20,
                'published_at' => now(),
            ],
            [
                'title' => 'Terms of Service',
                'slug' => 'terms-of-service',
                'content' => $this->getTermsOfServiceContent(),
                'excerpt' => 'Read our terms of service to understand the rules and regulations for using ShreeJi Jewelry website and services.',
                'meta_title' => 'Terms of Service - ShreeJi Jewelry',
                'meta_description' => 'Read our terms of service to understand the rules and regulations for using ShreeJi Jewelry website and services.',
                'meta_keywords' => ['terms of service', 'terms and conditions', 'legal'],
                'template' => 'legal',
                'is_published' => true,
                'show_in_menu' => false,
                'menu_order' => 21,
                'published_at' => now(),
            ],
            [
                'title' => 'Cookie Policy',
                'slug' => 'cookie-policy',
                'content' => $this->getCookiePolicyContent(),
                'excerpt' => 'Learn about how we use cookies and similar technologies to enhance your browsing experience on ShreeJi Jewelry website.',
                'meta_title' => 'Cookie Policy - ShreeJi Jewelry',
                'meta_description' => 'Learn about how we use cookies and similar technologies to enhance your browsing experience on ShreeJi Jewelry website.',
                'meta_keywords' => ['cookie policy', 'cookies', 'tracking', 'privacy'],
                'template' => 'legal',
                'is_published' => true,
                'show_in_menu' => false,
                'menu_order' => 22,
                'published_at' => now(),
            ],
        ];

        foreach ($pages as $pageData) {
            Page::updateOrCreate(
                ['slug' => $pageData['slug']],
                $pageData
            );
        }

        $this->command->info('Pages seeded successfully!');
    }

    private function getAboutContent(): string
    {
        return '<h2>Our Story</h2>
<p>For over three decades, ShreeJi has been crafting exquisite jewelry that celebrates life\'s most precious moments. Our journey began with a simple vision: to create timeless pieces that tell your unique story.</p>

<h3>Our Heritage</h3>
<p>Founded in 1990, ShreeJi Jewelry has grown from a small family business to one of India\'s most trusted jewelry brands. Our commitment to quality, craftsmanship, and customer satisfaction has remained unwavering throughout our journey.</p>

<h3>Our Craftsmanship</h3>
<p>Every piece of jewelry at ShreeJi is meticulously crafted by skilled artisans who have inherited their craft through generations. We combine traditional techniques with modern technology to create jewelry that is both timeless and contemporary.</p>

<h3>Our Values</h3>
<ul>
<li><strong>Quality:</strong> We use only the finest materials and maintain the highest standards of craftsmanship.</li>
<li><strong>Trust:</strong> We believe in transparent business practices and honest relationships with our customers.</li>
<li><strong>Innovation:</strong> We continuously evolve our designs to meet changing fashion trends while preserving traditional aesthetics.</li>
<li><strong>Service:</strong> We are committed to providing exceptional customer service at every step of your jewelry journey.</li>
</ul>

<h3>Our Promise</h3>
<p>At ShreeJi, we promise to deliver jewelry that not only enhances your beauty but also becomes a cherished part of your life\'s most important moments. Every piece is created with love, care, and attention to detail that you deserve.</p>';
    }

    private function getContactContent(): string
    {
        $phone = config('contact.phone');
        $email = config('contact.email');
        $address = config('contact.address.full');
        $businessHours = config('contact.business_hours.display');
        $phoneFormatted = '+91-' . substr($phone, 0, 5) . '-' . substr($phone, 5);

        return '<h2>Get in Touch</h2>
<p>We\'d love to hear from you. Get in touch with our team for any questions about our jewelry collection, custom designs, or services.</p>

<h3>Visit Our Store</h3>
<p>Experience our jewelry collection in person at our beautiful showroom located in the heart of Mumbai.</p>
<p><strong>Address:</strong><br>
ShreeJi Jewelry<br>
' . $address . '</p>

<h3>Contact Information</h3>
<p><strong>Phone:</strong> <a href="tel:+91' . $phone . '">' . $phoneFormatted . '</a><br>
<strong>Email:</strong> <a href="mailto:' . $email . '">' . $email . '</a><br>
<strong>WhatsApp:</strong> <a href="https://wa.me/91' . $phone . '" target="_blank">' . $phoneFormatted . '</a><br>
<strong>Instagram:</strong> <a href="' . config('contact.social.instagram.url') . '" target="_blank">@' . config('contact.social.instagram.username') . '</a></p>

<h3>Store Hours</h3>
<p>' . $businessHours . '</p>

<h3>Customer Service</h3>
<p>Our customer service team is available to assist you with:</p>
<ul>
<li>Product inquiries and recommendations</li>
<li>Custom jewelry design consultations</li>
<li>Order status and tracking</li>
<li>Returns and exchanges</li>
<li>Jewelry care and maintenance</li>
<li>Warranty and repair services</li>
</ul>

<h3>Follow Us</h3>
<p>Stay connected with us on social media for the latest updates, new arrivals, and exclusive offers:</p>
<ul>
<li><strong>Instagram:</strong> <a href="' . config('contact.social.instagram.url') . '" target="_blank">@' . config('contact.social.instagram.username') . '</a></li>
<li><strong>Facebook:</strong> <a href="' . config('contact.social.facebook.url') . '" target="_blank">@' . config('contact.social.facebook.username') . '</a></li>
<li><strong>YouTube:</strong> <a href="' . config('contact.social.youtube.url') . '" target="_blank">@' . config('contact.social.youtube.username') . '</a></li>
</ul>';
    }

    private function getShippingContent(): string
    {
        return '<h2>Shipping Information</h2>
<p>Fast, secure, and reliable delivery for your precious jewelry.</p>

<h3>Domestic Shipping (India)</h3>
<ul>
<li><strong>Free Shipping:</strong> On orders above ₹5,000</li>
<li><strong>Standard Shipping:</strong> ₹150 for orders ₹2,000 - ₹4,999</li>
<li><strong>Express Shipping:</strong> ₹250 for orders below ₹2,000</li>
<li><strong>Delivery Time:</strong> 2-5 business days for major cities, 3-7 days for remote areas</li>
</ul>

<h3>International Shipping</h3>
<p>We ship to selected countries worldwide:</p>
<ul>
<li>USA & Canada: 7-14 business days</li>
<li>UK & Europe: 5-10 business days</li>
<li>Australia: 7-12 business days</li>
<li>UAE & Middle East: 3-7 business days</li>
</ul>

<h3>Shipping Process</h3>
<ol>
<li><strong>Order Processing:</strong> 1-2 business days for verification and preparation</li>
<li><strong>Quality Check:</strong> Each piece is carefully inspected before packaging</li>
<li><strong>Secure Packaging:</strong> Premium packaging with bubble wrap and jewelry boxes</li>
<li><strong>Dispatch & Tracking:</strong> Tracking details sent via email and SMS</li>
</ol>

<h3>Order Tracking</h3>
<p>Track your order easily through:</p>
<ul>
<li>Your account dashboard</li>
<li>Tracking link sent to your email</li>
<li>Customer service hotline</li>
<li>WhatsApp support</li>
</ul>';
    }

    private function getReturnsContent(): string
    {
        return '<h2>Returns & Exchange Policy</h2>
<p>Hassle-free returns and exchanges for your peace of mind.</p>

<h3>Return Eligibility</h3>
<h4>✓ Eligible for Return:</h4>
<ul>
<li>Items in original condition with tags</li>
<li>Unworn jewelry with original packaging</li>
<li>Items returned within 30 days of delivery</li>
<li>Items with original certificates and warranty cards</li>
</ul>

<h4>✗ Not Eligible for Return:</h4>
<ul>
<li>Customized or personalized jewelry</li>
<li>Pierced earrings (for hygiene reasons)</li>
<li>Items damaged due to misuse</li>
<li>Items without original packaging</li>
<li>Sale items (unless defective)</li>
</ul>

<h3>Return Process</h3>
<ol>
<li><strong>Initiate Return:</strong> Login to your account and request return from "My Orders"</li>
<li><strong>Pack Securely:</strong> Pack item in original packaging with all accessories</li>
<li><strong>Schedule Pickup:</strong> We\'ll arrange free pickup from your address</li>
<li><strong>Get Refund:</strong> Refund processed within 5-7 business days</li>
</ol>

<h3>Exchange Policy</h3>
<p><strong>Size Exchange:</strong> Free size exchange for rings and bangles within 30 days.</p>
<p><strong>Design Exchange:</strong> Exchange for same or higher value item with price difference payment.</p>

<h3>Refund Timeline</h3>
<ul>
<li>Credit/Debit Card: 5-7 business days</li>
<li>Net Banking: 5-7 business days</li>
<li>UPI/Wallet: 3-5 business days</li>
<li>Cash on Delivery: 7-10 business days (₹50 processing fee)</li>
</ul>';
    }

    private function getSizeGuideContent(): string
    {
        return '<h2>Size Guide</h2>
<p>Find your perfect fit with our comprehensive sizing guide.</p>

<h3>Ring Size Guide</h3>
<h4>How to Measure:</h4>
<ol>
<li>Wrap a string around your finger where you\'d wear the ring</li>
<li>Mark where the string overlaps</li>
<li>Measure the string length with a ruler</li>
<li>Use the chart below to find your ring size</li>
</ol>

<h4>Ring Size Chart</h4>
<table>
<tr><th>Indian Size</th><th>US Size</th><th>UK Size</th><th>Circumference (mm)</th></tr>
<tr><td>8</td><td>4</td><td>H</td><td>46.8</td></tr>
<tr><td>10</td><td>5</td><td>J</td><td>49.3</td></tr>
<tr><td>12</td><td>6</td><td>L</td><td>51.8</td></tr>
<tr><td>14</td><td>7</td><td>N</td><td>54.4</td></tr>
<tr><td>16</td><td>8</td><td>P</td><td>56.9</td></tr>
<tr><td>18</td><td>9</td><td>R</td><td>59.5</td></tr>
<tr><td>20</td><td>10</td><td>T</td><td>62.1</td></tr>
</table>

<h3>Bangle Size Guide</h3>
<p>Measure the widest part of your hand when thumb and little finger are brought together.</p>

<h3>Necklace Length Guide</h3>
<ul>
<li><strong>14-16" (Choker):</strong> Sits at the base of neck</li>
<li><strong>16-18" (Princess):</strong> Sits at collarbone - most versatile</li>
<li><strong>20-24" (Matinee):</strong> Falls below collarbone</li>
<li><strong>28-36" (Opera):</strong> Falls at or below bust line</li>
</ul>';
    }

    private function getJewelryCareContent(): string
    {
        return '<h2>Jewelry Care Guide</h2>
<p>Keep your precious jewelry sparkling for generations.</p>

<h3>Gold Jewelry Care</h3>
<h4>Do\'s:</h4>
<ul>
<li>Clean with warm soapy water</li>
<li>Use a soft-bristled toothbrush</li>
<li>Dry thoroughly after cleaning</li>
<li>Store in individual pouches</li>
<li>Remove before swimming</li>
</ul>

<h4>Don\'ts:</h4>
<ul>
<li>Use harsh chemicals or bleach</li>
<li>Wear while exercising</li>
<li>Store with other metals</li>
<li>Use ultrasonic cleaners on gemstones</li>
<li>Expose to perfumes directly</li>
</ul>

<h3>Silver Jewelry Care</h3>
<h4>Do\'s:</h4>
<ul>
<li>Polish regularly with silver cloth</li>
<li>Store in anti-tarnish pouches</li>
<li>Wear frequently to prevent tarnishing</li>
<li>Clean with silver-specific cleaners</li>
<li>Keep away from humidity</li>
</ul>

<h3>Gemstone Care</h3>
<ul>
<li><strong>Diamond:</strong> Warm soapy water, soft brush</li>
<li><strong>Ruby/Sapphire:</strong> Warm water, avoid sudden temperature changes</li>
<li><strong>Emerald:</strong> Gentle cleaning, avoid ultrasonic cleaners</li>
<li><strong>Pearl:</strong> Soft cloth only, avoid chemicals</li>
</ul>

<h3>Storage Tips</h3>
<ul>
<li>Store each piece separately in soft pouches</li>
<li>Keep in a cool, dry place away from sunlight</li>
<li>Use jewelry boxes with individual compartments</li>
<li>Regular inspection for loose stones or damaged clasps</li>
</ul>';
    }

    private function getWarrantyContent(): string
    {
        $phone = config('contact.phone');
        $email = config('contact.departments.warranty.email');
        $phoneFormatted = '+91-' . substr($phone, 0, 5) . '-' . substr($phone, 5);

        return '<h2>Warranty Information</h2>
<p>Comprehensive protection for your precious jewelry investment.</p>

<h3>Warranty Coverage</h3>
<h4>What\'s Covered:</h4>
<ul>
<li>Manufacturing defects in materials</li>
<li>Faulty craftsmanship issues</li>
<li>Loose or broken prongs</li>
<li>Defective clasps or closures</li>
<li>Stone setting problems</li>
<li>Metal discoloration (plated items)</li>
</ul>

<h4>Not Covered:</h4>
<ul>
<li>Normal wear and tear</li>
<li>Damage from misuse or abuse</li>
<li>Loss or theft</li>
<li>Damage from chemicals</li>
<li>Scratches on metal surfaces</li>
<li>Alterations by third parties</li>
</ul>

<h3>Warranty Terms</h3>
<ul>
<li><strong>Gold Jewelry (14K+):</strong> 1 Year</li>
<li><strong>Silver Jewelry:</strong> 1 Year</li>
<li><strong>Platinum Jewelry:</strong> 1 Year</li>
<li><strong>Diamond Jewelry:</strong> 1 Year (Setting & mounting issues)</li>
<li><strong>Gemstone Jewelry:</strong> 6 Months (Setting issues only)</li>
<li><strong>Fashion Jewelry:</strong> 3 Months</li>
</ul>

<h3>Warranty Claim Process</h3>
<ol>
<li>Contact our customer service team at <a href="tel:+91' . $phone . '">' . $phoneFormatted . '</a> or <a href="mailto:' . $email . '">' . $email . '</a></li>
<li>Provide purchase details and issue description</li>
<li>Send item for inspection (prepaid label provided)</li>
<li>Item repaired and returned within 7-10 business days</li>
</ol>

<h3>Contact Warranty Department</h3>
<p><strong>Phone:</strong> <a href="tel:+91' . $phone . '">' . $phoneFormatted . '</a><br>
<strong>Email:</strong> <a href="mailto:' . $email . '">' . $email . '</a><br>
<strong>WhatsApp:</strong> <a href="https://wa.me/91' . $phone . '" target="_blank">' . $phoneFormatted . '</a></p>

<h3>Lifetime Services</h3>
<p>Beyond warranty period, we offer:</p>
<ul>
<li>Complimentary cleaning service</li>
<li>Annual quality inspection</li>
<li>Minor repairs at discounted rates</li>
<li>Professional resizing services</li>
</ul>';
    }

    private function getPrivacyPolicyContent(): string
    {
        return '<h2>Privacy Policy</h2>
<p>Your privacy and data security are our top priorities.</p>

<h3>Information We Collect</h3>
<h4>Personal Information:</h4>
<ul>
<li>Name, email address, and phone number</li>
<li>Billing and shipping addresses</li>
<li>Payment information (processed securely)</li>
<li>Account credentials and preferences</li>
<li>Purchase history and order details</li>
</ul>

<h4>Automatically Collected Information:</h4>
<ul>
<li>IP address and browser information</li>
<li>Device type and operating system</li>
<li>Pages visited and time spent on site</li>
<li>Cookies and tracking technologies</li>
</ul>

<h3>How We Use Your Information</h3>
<ul>
<li>Process and fulfill orders</li>
<li>Provide customer support</li>
<li>Send marketing communications (with consent)</li>
<li>Improve our services and website</li>
<li>Prevent fraud and ensure security</li>
</ul>

<h3>Information Sharing</h3>
<p>We do not sell, trade, or rent your personal information to third parties. We may share information with:</p>
<ul>
<li>Service providers (payment processors, shipping companies)</li>
<li>Legal authorities when required by law</li>
<li>Business partners for legitimate business purposes</li>
</ul>

<h3>Data Security</h3>
<ul>
<li>SSL/TLS encryption for data transmission</li>
<li>Secure servers with access controls</li>
<li>Regular security audits and updates</li>
<li>Staff training on data protection</li>
</ul>

<h3>Your Rights</h3>
<ul>
<li>Access your personal information</li>
<li>Correct inaccurate information</li>
<li>Request deletion of your data</li>
<li>Opt-out of marketing communications</li>
</ul>

<h3>Contact Us About Privacy</h3>
<p>If you have questions about this Privacy Policy or our data practices, please contact us:</p>
<p><strong>Privacy Officer:</strong> <a href="mailto:' . config('contact.departments.privacy.email') . '">' . config('contact.departments.privacy.email') . '</a><br>
<strong>Phone:</strong> <a href="tel:+91' . config('contact.phone') . '">+91-' . substr(config('contact.phone'), 0, 5) . '-' . substr(config('contact.phone'), 5) . '</a><br>
<strong>Address:</strong> ' . config('contact.address.full') . '</p>';
    }

    private function getTermsOfServiceContent(): string
    {
        return '<h2>Terms of Service</h2>
<p>Please read these terms carefully before using our services.</p>

<h3>Agreement to Terms</h3>
<p>By accessing or using our website, you agree to be bound by these Terms of Service. If you disagree with any part of these terms, you may not access our website or use our services.</p>

<h3>Use of Our Service</h3>
<h4>Permitted Uses:</h4>
<ul>
<li>Browse and purchase jewelry products</li>
<li>Create and manage your account</li>
<li>Contact customer service for support</li>
<li>Leave reviews and feedback</li>
</ul>

<h4>Prohibited Uses:</h4>
<ul>
<li>Violate any applicable laws or regulations</li>
<li>Transmit harmful or malicious code</li>
<li>Attempt unauthorized access to our systems</li>
<li>Interfere with or disrupt our service</li>
<li>Engage in fraudulent activities</li>
</ul>

<h3>Account Terms</h3>
<ul>
<li>You must provide accurate and complete information</li>
<li>You must be at least 18 years old to create an account</li>
<li>You are responsible for maintaining account security</li>
<li>One account per person is allowed</li>
</ul>

<h3>Purchase Terms</h3>
<ul>
<li>All orders subject to acceptance</li>
<li>Prices may change without notice</li>
<li>Payment required before shipping</li>
<li>30-day return policy applies</li>
</ul>

<h3>Intellectual Property</h3>
<p>All content on our website is owned by ShreeJi Jewelry and protected by copyright and trademark laws. You may not use, reproduce, or distribute our content without written permission.</p>

<h3>Limitation of Liability</h3>
<p>To the maximum extent permitted by law, ShreeJi Jewelry shall not be liable for any indirect, incidental, special, or consequential damages arising from your use of our service.</p>

<h3>Governing Law</h3>
<p>These Terms shall be governed by the laws of India. Any disputes shall be subject to the exclusive jurisdiction of the courts in Mumbai, India.</p>';
    }

    private function getCookiePolicyContent(): string
    {
        return '<h2>Cookie Policy</h2>
<p>Understanding how we use cookies to improve your experience.</p>

<h3>What Are Cookies?</h3>
<p>Cookies are small text files stored on your device when you visit our website. They help us provide you with a better browsing experience by remembering your preferences and analyzing how you use our site.</p>

<h3>Types of Cookies We Use</h3>
<h4>Essential Cookies:</h4>
<ul>
<li>Authentication and security cookies</li>
<li>Shopping cart functionality</li>
<li>Session management</li>
<li>CSRF protection</li>
</ul>

<h4>Functional Cookies:</h4>
<ul>
<li>Language and region preferences</li>
<li>Currency selection</li>
<li>Recently viewed products</li>
<li>Wishlist items</li>
</ul>

<h4>Analytics Cookies:</h4>
<ul>
<li>Page views and user journeys</li>
<li>Time spent on pages</li>
<li>Popular products and categories</li>
<li>Device and browser information</li>
</ul>

<h4>Marketing Cookies:</h4>
<ul>
<li>Personalized product recommendations</li>
<li>Retargeting advertisements</li>
<li>Social media integration</li>
<li>Conversion tracking</li>
</ul>

<h3>Third-Party Cookies</h3>
<p>We use services from trusted third parties:</p>
<ul>
<li><strong>Google Analytics:</strong> Website analytics</li>
<li><strong>Facebook Pixel:</strong> Social media marketing</li>
<li><strong>Razorpay:</strong> Payment processing</li>
</ul>

<h3>Managing Cookies</h3>
<p>You can control cookies through:</p>
<ul>
<li>Your browser settings</li>
<li>Our cookie consent banner</li>
<li>Third-party opt-out tools</li>
</ul>

<h3>Impact of Disabling Cookies</h3>
<p>Disabling certain cookies may affect your experience:</p>
<ul>
<li>You may need to re-enter information repeatedly</li>
<li>Some features may not work properly</li>
<li>Shopping cart may not function correctly</li>
<li>Less relevant content and advertisements</li>
</ul>

<h3>Questions About Cookies</h3>
<p>If you have questions about our use of cookies, please contact us:</p>
<p><strong>Privacy Team:</strong> <a href="mailto:' . config('contact.departments.privacy.email') . '">' . config('contact.departments.privacy.email') . '</a><br>
<strong>Phone:</strong> <a href="tel:+91' . config('contact.phone') . '">+91-' . substr(config('contact.phone'), 0, 5) . '-' . substr(config('contact.phone'), 5) . '</a></p>';
    }
}
