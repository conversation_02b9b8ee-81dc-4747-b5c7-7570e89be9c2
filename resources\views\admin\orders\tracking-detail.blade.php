@extends('layouts.admin')

@section('title', 'Order Tracking Details - ' . $order->order_number)

@section('content')
<!-- Page Header -->
<div class="row align-items-center mb-3 mb-md-4">
    <div class="col-12">
        <div class="d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center gap-3">
            <div>
                <h1 class="h4 h-md-3 mb-1 text-gray-800">Order Details</h1>
                <!-- Mobile Breadcrumb -->
                <nav aria-label="breadcrumb" class="d-md-none">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}"><i class="fas fa-home"></i></a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.orders.tracking.index') }}">Tracking</a></li>
                        <li class="breadcrumb-item active">{{ Str::limit($order->order_number, 15) }}</li>
                    </ol>
                </nav>
                <!-- Desktop Breadcrumb -->
                <nav aria-label="breadcrumb" class="d-none d-md-block">
                    <ol class="breadcrumb mb-0">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.orders.tracking.index') }}">Order Tracking</a></li>
                        <li class="breadcrumb-item active">{{ $order->order_number }}</li>
                    </ol>
                </nav>
            </div>
            <div class="d-flex flex-column flex-sm-row gap-2 w-100 w-md-auto">
                <button class="btn btn-primary btn-sm" onclick="updateStatus({{ $order->id }})">
                    <i class="fas fa-edit me-1"></i>
                    <span class="d-none d-sm-inline">Update Status</span>
                    <span class="d-sm-none">Update</span>
                </button>
                @if(!$order->tracking_number)
                <button class="btn btn-success btn-sm" onclick="addTracking({{ $order->id }})">
                    <i class="fas fa-plus me-1"></i>
                    <span class="d-none d-sm-inline">Add Tracking</span>
                    <span class="d-sm-none">Track</span>
                </button>
                @endif
            </div>
        </div>
    </div>
</div>

    <div class="row">
        <!-- Order Information -->
        <div class="col-lg-8">
            <!-- Order Summary -->
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">Order Summary</h6>
                    <span class="badge {{ $order->getStatusBadgeClass() }} fs-6">
                        {{ $order->getStatusDisplayName() }}
                    </span>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Order Number:</strong></td>
                                    <td>{{ $order->order_number }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Customer:</strong></td>
                                    <td>{{ $order->user->name }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Email:</strong></td>
                                    <td>{{ $order->user->email }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Phone:</strong></td>
                                    <td>{{ $order->user->phone ?? 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Order Date:</strong></td>
                                    <td>{{ $order->created_at->format('M d, Y H:i A') }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Total Amount:</strong></td>
                                    <td class="text-success fw-bold">₹{{ number_format($order->total_amount, 2) }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Payment Status:</strong></td>
                                    <td>
                                        <span class="badge {{ $order->payment_status == 'paid' ? 'bg-success' : 'bg-warning' }}">
                                            {{ ucfirst($order->payment_status) }}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td><strong>Payment Method:</strong></td>
                                    <td>{{ ucfirst($order->payment_method ?? 'N/A') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Last Updated:</strong></td>
                                    <td>{{ $order->updated_at->format('M d, Y H:i A') }}</td>
                                </tr>
                                <tr>
                                    <td><strong>Updated By:</strong></td>
                                    <td>{{ $order->updated_by ?? 'System' }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tracking Information -->
            @if($order->tracking_number)
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Tracking Information</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <strong>Tracking Number:</strong>
                            <p class="mb-2">
                                <a href="{{ $order->tracking_url }}" target="_blank" class="text-decoration-none">
                                    {{ $order->tracking_number }}
                                    <i class="fas fa-external-link-alt ms-1"></i>
                                </a>
                            </p>
                        </div>
                        <div class="col-md-4">
                            <strong>Courier Service:</strong>
                            <p class="mb-2">{{ $order->courier_service }}</p>
                        </div>
                        <div class="col-md-4">
                            <strong>Shipped Date:</strong>
                            <p class="mb-2">{{ $order->shipped_at ? $order->shipped_at->format('M d, Y H:i A') : 'N/A' }}</p>
                        </div>
                    </div>
                </div>
            </div>
            @endif

            <!-- Order Items -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Order Items</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>SKU</th>
                                    <th>Size</th>
                                    <th>Quantity</th>
                                    <th>Price</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($order->orderItems as $item)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            @if($item->product->images && count($item->product->images) > 0)
                                                <img src="{{ $item->product->images[0] }}" alt="{{ $item->product->name }}" 
                                                     class="me-3" style="width: 50px; height: 50px; object-fit: cover; border-radius: 5px;">
                                            @endif
                                            <div>
                                                <strong>{{ $item->product->name }}</strong>
                                                <br><small class="text-muted">{{ $item->product->category->name }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ $item->product->sku }}</td>
                                    <td>{{ $item->size ?? 'N/A' }}</td>
                                    <td>{{ $item->quantity }}</td>
                                    <td>₹{{ number_format($item->price, 2) }}</td>
                                    <td>₹{{ number_format($item->total_price, 2) }}</td>
                                </tr>
                                @endforeach
                            </tbody>
                            <tfoot>
                                <tr>
                                    <th colspan="5" class="text-end">Subtotal:</th>
                                    <th>₹{{ number_format($order->subtotal, 2) }}</th>
                                </tr>
                                @if($order->discount_amount > 0)
                                <tr>
                                    <th colspan="5" class="text-end">Discount:</th>
                                    <th class="text-success">-₹{{ number_format($order->discount_amount, 2) }}</th>
                                </tr>
                                @endif
                                <tr>
                                    <th colspan="5" class="text-end">Tax:</th>
                                    <th>₹{{ number_format($order->tax_amount, 2) }}</th>
                                </tr>
                                <tr>
                                    <th colspan="5" class="text-end">Shipping:</th>
                                    <th>₹{{ number_format($order->shipping_amount, 2) }}</th>
                                </tr>
                                <tr class="table-primary">
                                    <th colspan="5" class="text-end">Total:</th>
                                    <th>₹{{ number_format($order->total_amount, 2) }}</th>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Admin Notes -->
            @if($order->admin_notes || $order->notes)
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Notes</h6>
                </div>
                <div class="card-body">
                    @if($order->admin_notes)
                    <div class="mb-3">
                        <strong>Admin Notes:</strong>
                        <p class="mb-0">{{ $order->admin_notes }}</p>
                    </div>
                    @endif
                    @if($order->notes)
                    <div>
                        <strong>Customer Notes:</strong>
                        <p class="mb-0">{{ $order->notes }}</p>
                    </div>
                    @endif
                </div>
            </div>
            @endif
        </div>

        <!-- Status Timeline -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Status Timeline</h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        @if($order->status_history)
                            @foreach(array_reverse($order->status_history) as $history)
                            <div class="timeline-item mb-3">
                                <div class="d-flex">
                                    <div class="timeline-marker me-3">
                                        <i class="fas fa-circle text-primary"></i>
                                    </div>
                                    <div class="timeline-content">
                                        <h6 class="mb-1">{{ ucfirst(str_replace('_', ' ', $history['status'])) }}</h6>
                                        <small class="text-muted">{{ \Carbon\Carbon::parse($history['timestamp'])->format('M d, Y H:i A') }}</small>
                                        @if(isset($history['updated_by']))
                                        <br><small class="text-muted">by {{ $history['updated_by'] }}</small>
                                        @endif
                                        @if(isset($history['notes']) && $history['notes'])
                                        <p class="mt-2 mb-0 small">{{ $history['notes'] }}</p>
                                        @endif
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        @else
                        <div class="timeline-item">
                            <div class="d-flex">
                                <div class="timeline-marker me-3">
                                    <i class="fas fa-circle text-primary"></i>
                                </div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">Order Created</h6>
                                    <small class="text-muted">{{ $order->created_at->format('M d, Y H:i A') }}</small>
                                </div>
                            </div>
                        </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card shadow">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        @foreach($order->getNextPossibleStatuses() as $status)
                        <button class="btn btn-outline-primary btn-sm" onclick="quickStatusUpdate('{{ $status }}')">
                            Mark as {{ ucfirst(str_replace('_', ' ', $status)) }}
                        </button>
                        @endforeach
                        
                        @if($order->tracking_number)
                        <a href="{{ $order->tracking_url }}" target="_blank" class="btn btn-outline-info btn-sm">
                            <i class="fas fa-external-link-alt me-2"></i>Track Package
                        </a>
                        @endif
                        
                        <button class="btn btn-outline-secondary btn-sm" onclick="printOrder()">
                            <i class="fas fa-print me-2"></i>Print Order
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@push('styles')
<style>
/* Timeline Styles */
.timeline {
    position: relative;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 8px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e9ecef;
}

.timeline-marker {
    position: relative;
    z-index: 1;
    background: white;
}

.timeline-content {
    flex: 1;
}

/* Mobile-First Order Detail Styles */
@media (max-width: 767.98px) {
    .h4 {
        font-size: 1.25rem !important;
    }

    .breadcrumb {
        font-size: 0.85rem;
    }

    .breadcrumb-item + .breadcrumb-item::before {
        font-size: 0.75rem;
    }

    .card-header h6 {
        font-size: 1rem;
    }

    .table-borderless td {
        padding: 0.5rem 0;
        font-size: 0.9rem;
    }

    .table-borderless strong {
        font-size: 0.85rem;
    }

    .badge {
        font-size: 0.7rem;
        padding: 0.3rem 0.6rem;
    }

    .timeline-item {
        margin-bottom: 1rem !important;
    }

    .timeline-content h6 {
        font-size: 0.95rem;
    }

    .timeline-content small {
        font-size: 0.8rem;
    }

    .timeline-content p {
        font-size: 0.85rem;
    }

    .btn-sm {
        padding: 0.375rem 0.75rem;
        font-size: 0.85rem;
    }

    .d-grid .btn {
        margin-bottom: 0.5rem;
    }
}

/* Mobile Table Improvements */
@media (max-width: 767.98px) {
    .table-responsive {
        font-size: 0.8rem;
    }

    .table th,
    .table td {
        padding: 0.5rem 0.25rem;
        vertical-align: middle;
    }

    .table img {
        width: 35px !important;
        height: 35px !important;
    }

    .table tfoot th {
        font-size: 0.85rem;
    }
}

/* Mobile Card Improvements */
@media (max-width: 767.98px) {
    .card {
        margin-bottom: 1rem;
    }

    .card-body {
        padding: 1rem;
    }

    .card-header {
        padding: 0.75rem 1rem;
    }
}

/* Mobile Row Adjustments */
@media (max-width: 991.98px) {
    .col-lg-8,
    .col-lg-4 {
        margin-bottom: 1rem;
    }
}

/* Touch-friendly improvements */
@media (max-width: 767.98px) {
    .btn {
        min-height: 44px;
    }

    .btn-sm {
        min-height: 36px;
    }
}

/* Mobile notification positioning */
@media (max-width: 767.98px) {
    .alert.position-fixed {
        top: 70px !important;
        left: 10px !important;
        right: 10px !important;
        width: auto !important;
        min-width: auto !important;
    }
}
</style>
@endpush

@push('scripts')
<script>
function updateStatus(orderId) {
    // Reuse the modal from tracking.blade.php
    fetch(`/admin/orders/tracking/${orderId}/info`)
        .then(response => response.json())
        .then(data => {
            // Create a simple prompt for now, or redirect to main tracking page
            const newStatus = prompt('Enter new status (confirmed, processing, packed, shipped, out_for_delivery, delivered):');
            if (newStatus) {
                const notes = prompt('Enter notes (optional):');

                fetch(`/admin/orders/tracking/${orderId}/status`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        status: newStatus,
                        notes: notes
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showAlert(data.message, 'success');
                        location.reload();
                    } else {
                        showAlert(data.message, 'danger');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showAlert('Error updating status', 'danger');
                });
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('Error fetching order details', 'danger');
        });
}

function addTracking(orderId) {
    const trackingNumber = prompt('Enter tracking number:');
    if (trackingNumber) {
        const courierService = prompt('Enter courier service (default: India Post):') || 'India Post';
        const notes = prompt('Enter notes (optional):');

        fetch(`/admin/orders/tracking/${orderId}/tracking`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                tracking_number: trackingNumber,
                courier_service: courierService,
                notes: notes
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(data.message, 'success');
                location.reload();
            } else {
                showAlert(data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('Error adding tracking information', 'danger');
        });
    }
}

function quickStatusUpdate(status) {
    const orderId = {{ $order->id }};
    const notes = prompt(`Mark order as ${status.replace('_', ' ')}. Add notes (optional):`);

    fetch(`/admin/orders/tracking/${orderId}/status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            status: status,
            notes: notes
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert(data.message, 'success');
            location.reload();
        } else {
            showAlert(data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showAlert('Error updating status', 'danger');
    });
}

function printOrder() {
    window.print();
}

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(alertDiv);

    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}
</script>
@endpush

@endsection
