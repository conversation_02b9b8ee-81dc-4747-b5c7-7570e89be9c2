
<!-- Footer -->
<footer class="footer mt-auto">
    <div class="container">
        <!-- Newsletter Section -->
        <div class="row justify-content-center mb-5">
            <div class="col-lg-8 col-md-10">
                <div class="newsletter-section text-center">
                    <h4 class="font-playfair text-white mb-3">Stay Updated with Our Latest Collections</h4>
                    <p class="text-light mb-4">Subscribe to our newsletter and be the first to know about new arrivals, exclusive offers, and jewelry care tips.</p>
                    <form id="newsletter-form" class="newsletter-form">
                        <div class="input-group">
                            <input type="email" class="form-control" placeholder="Enter your email address" required>
                            <button class="btn" type="submit" style="background-color: var(--primary-cream); color: var(--primary-brown); border: none;">
                                <i class="fas fa-paper-plane me-2"></i>Subscribe
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Main Footer Content -->
        <div class="row g-4 mb-4">
            <!-- Brand Section -->
            <div class="col-lg-4 col-md-6">
                <h5 class="font-playfair mb-4">
                    <i class="fas fa-gem me-2"></i>ShreeJi
                </h5>
                <p class="mb-4">Crafting exquisite jewelry with passion and precision. Each piece tells a story of timeless elegance and exceptional artistry.</p>
                <div class="social-icons">
                    @foreach($contact['social'] as $platform => $details)
                    <a href="{{ $details['url'] }}" target="_blank" title="{{ ucfirst($platform) }}" aria-label="{{ ucfirst($platform) }}">
                        <i class="fab fa-{{ $platform === 'facebook' ? 'facebook-f' : $platform }}"></i>
                    </a>
                    @endforeach
                </div>
            </div>

            <!-- Quick Links -->
            <div class="col-lg-2 col-md-6 col-6">
                <h5>Quick Links</h5>
                <ul class="list-unstyled footer-links">
                    <li><a href="{{ url('/') }}">Home</a></li>
                    <li><a href="{{ url('/collections') }}">Collections</a></li>
                    @if(isset($menuPages))
                        @foreach($menuPages as $page)
                        <li><a href="{{ route('page.show', $page->slug) }}">{{ $page->title }}</a></li>
                        @endforeach
                    @else
                        <li><a href="{{ url('/about') }}">About Us</a></li>
                        <li><a href="{{ url('/contact') }}">Contact</a></li>
                    @endif
                </ul>
            </div>

            <!-- Categories -->
            <div class="col-lg-2 col-md-6 col-6">
                <h5>Categories</h5>
                <ul class="list-unstyled footer-links">
                    @if(isset($globalCategories) && $globalCategories->count() > 0)
                        @foreach($globalCategories->take(4) as $category)
                        <li><a href="{{ route('collections.category', $category->slug) }}">{{ $category->name }}</a></li>
                        @endforeach
                    @else
                        <li><a href="{{ url('/collections/rings') }}">Rings</a></li>
                        <li><a href="{{ url('/collections/necklaces') }}">Necklaces</a></li>
                        <li><a href="{{ url('/collections/earrings') }}">Earrings</a></li>
                        <li><a href="{{ url('/collections/bracelets') }}">Bracelets</a></li>
                    @endif
                </ul>
            </div>

            <!-- Customer Care -->
            <div class="col-lg-2 col-md-6 col-6">
                <h5>Customer Care</h5>
                <ul class="list-unstyled footer-links">
                    @if(isset($footerPages['guide']))
                        @foreach($footerPages['guide'] as $page)
                        <li><a href="{{ route('page.show', $page->slug) }}">{{ $page->title }}</a></li>
                        @endforeach
                    @else
                        <li><a href="{{ url('/shipping') }}">Shipping Info</a></li>
                        <li><a href="{{ url('/returns') }}">Returns</a></li>
                        <li><a href="{{ url('/size-guide') }}">Size Guide</a></li>
                        <li><a href="{{ url('/warranty') }}">Warranty</a></li>
                    @endif
                </ul>
            </div>

            <!-- Contact Info -->
            <div class="col-lg-2 col-md-6 col-6">
                <h5>Contact Info</h5>
                <ul class="list-unstyled footer-contact">
                    <li><i class="fas fa-map-marker-alt me-2"></i>{{ $contact['address'] }}</li>
                    <li><i class="fas fa-phone me-2"></i><a href="tel:{{ $contact['phoneLink'] }}" class="text-decoration-none">{{ $contact['phoneFormatted'] }}</a></li>
                    <li><i class="fas fa-envelope me-2"></i><a href="mailto:{{ $contact['email'] }}" class="text-decoration-none">{{ $contact['email'] }}</a></li>
                    <li><i class="fab fa-whatsapp me-2"></i><a href="{{ $contact['whatsappLink'] }}" target="_blank" class="text-decoration-none">WhatsApp</a></li>
                    <li><i class="fas fa-clock me-2"></i>{{ $contact['businessHours'] }}</li>
                </ul>
            </div>
        </div>

        <!-- Footer Bottom -->
        <div class="footer-bottom">
            <hr class="footer-divider">
            <div class="row align-items-center">
                <div class="col-md-6 text-center text-md-start">
                    <p class="mb-0">&copy; {{ date('Y') }} ShreeJi Jewelry. All rights reserved.</p>
                </div>
                <div class="col-md-6 text-center text-md-end mt-3 mt-md-0">
                    <div class="footer-legal-links">
                        @if(isset($footerPages['legal']))
                            @foreach($footerPages['legal'] as $page)
                            <a href="{{ route('page.show', $page->slug) }}">{{ $page->title }}</a>
                            @endforeach
                        @endif
                        @if(isset($footerPages['policy']))
                            @foreach($footerPages['policy'] as $page)
                            <a href="{{ route('page.show', $page->slug) }}">{{ $page->title }}</a>
                            @endforeach
                        @endif
                        @if(!isset($footerPages['legal']) && !isset($footerPages['policy']))
                            <a href="{{ url('/privacy-policy') }}">Privacy Policy</a>
                            <a href="{{ url('/terms-of-service') }}">Terms of Service</a>
                            <a href="{{ url('/cookie-policy') }}">Cookie Policy</a>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</footer>

<style>
/* Additional Footer Styles */
@media (max-width: 575.98px) {
    .footer .col-6 {
        margin-bottom: 2rem;
    }

    .footer .col-6:nth-child(odd) {
        padding-right: 0.75rem;
    }

    .footer .col-6:nth-child(even) {
        padding-left: 0.75rem;
    }
}

@media (min-width: 576px) and (max-width: 767.98px) {
    .footer .col-md-6 {
        margin-bottom: 1.5rem;
    }
}

/* Ensure footer sticks to bottom */
.footer {
    margin-top: auto;
}

/* Newsletter form improvements */
.newsletter-form .form-control:focus {
    box-shadow: 0 0 0 0.2rem rgba(139, 69, 19, 0.25);
    border-color: var(--primary-brown);
}

/* Touch-friendly footer links */
@media (max-width: 991.98px) {
    .footer-links a,
    .footer-legal-links a {
        min-height: 44px;
        display: flex;
        align-items: center;
        padding: 8px 0;
    }

    .social-icons a {
        min-width: 44px;
        min-height: 44px;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Newsletter form submission
    const newsletterForm = document.getElementById('newsletter-form');
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const email = this.querySelector('input[type="email"]').value;
            const button = this.querySelector('button[type="submit"]');
            const originalText = button.innerHTML;

            if (email) {
                // Show loading state
                button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Subscribing...';
                button.disabled = true;

                // Simulate API call (replace with actual API call)
                setTimeout(() => {
                    // Show success message
                    const alertDiv = document.createElement('div');
                    alertDiv.className = 'alert alert-success alert-dismissible fade show mt-3';
                    alertDiv.innerHTML = `
                        <i class="fas fa-check-circle me-2"></i>
                        Thank you for subscribing! We'll keep you updated with our latest collections.
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    `;

                    this.appendChild(alertDiv);
                    this.reset();

                    // Reset button
                    button.innerHTML = originalText;
                    button.disabled = false;

                    // Auto-hide alert after 5 seconds
                    setTimeout(() => {
                        if (alertDiv.parentNode) {
                            alertDiv.remove();
                        }
                    }, 5000);
                }, 1000);
            }
        });
    }

    // Smooth scroll for footer links
    document.querySelectorAll('.footer a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                const navbarHeight = document.querySelector('.navbar').offsetHeight;
                const targetPosition = target.offsetTop - navbarHeight;
                window.scrollTo({
                    top: targetPosition,
                    behavior: 'smooth'
                });
            }
        });
    });
});
</script>
