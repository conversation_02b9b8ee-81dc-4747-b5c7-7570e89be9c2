<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;
use App\Models\Order;

class OrderStatusUpdate extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    public $order;
    public $status;
    public $trackingNumber;

    /**
     * Create a new message instance.
     */
    public function __construct(Order $order, $status, $trackingNumber = null)
    {
        $this->order = $order;
        $this->status = $status;
        $this->trackingNumber = $trackingNumber;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $statusTitles = [
            'confirmed' => 'Order Confirmed',
            'processing' => 'Order Being Processed',
            'shipped' => 'Order Shipped',
            'delivered' => 'Order Delivered',
            'cancelled' => 'Order Cancelled',
        ];

        $subject = ($statusTitles[$this->status] ?? 'Order Update') . ' - ' . $this->order->order_number;

        return new Envelope(
            subject: $subject,
            from: config('mail.from.address'),
            replyTo: config('mail.from.address'),
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.order-status-update',
            with: [
                'order' => $this->order,
                'status' => $this->status,
                'trackingNumber' => $this->trackingNumber,
                'customer' => $this->order->user,
                'orderItems' => $this->order->orderItems()->with('product')->get(),
                'billingAddress' => $this->order->billing_address,
                'shippingAddress' => $this->order->shipping_address,
            ]
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
