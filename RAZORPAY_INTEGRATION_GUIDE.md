# Razorpay Integration Guide

## Overview
This guide covers the complete Razorpay payment gateway integration with Laravel email notifications for your e-commerce application.

## Features Implemented
- ✅ Razorpay Test API Integration
- ✅ Order Creation and Payment Processing
- ✅ Payment Verification with Signature Validation
- ✅ Webhook Support for Payment Events
- ✅ Email Notifications on Successful Purchase
- ✅ Support for Multiple Payment Methods (Card, UPI, Net Banking)
- ✅ Cash on Delivery (COD) Option
- ✅ Responsive Email Templates
- ✅ Error Handling and Logging

## Installation & Configuration

### 1. Environment Configuration
The following environment variables have been configured in your `.env` file:

```env
# Razorpay Configuration (Test Mode)
RAZORPAY_KEY_ID=rzp_test_1DP5mmOlF5G5ag
RAZORPAY_KEY_SECRET=thisissecretkey
RAZORPAY_WEBHOOK_SECRET=whsec_test_webhook_secret

# Email Configuration
MAIL_MAILER=smtp
MAIL_HOST=sandbox.smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=your_mailtrap_username
MAIL_PASSWORD=your_mailtrap_password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"
```

### 2. Database Migration
Run the migration to add Razorpay-specific fields to the orders table:
```bash
php artisan migrate
```

## Files Created/Modified

### New Files Created:
1. `app/Services/RazorpayService.php` - Core Razorpay service class
2. `app/Http/Controllers/PaymentController.php` - Payment handling controller
3. `app/Mail/OrderConfirmation.php` - Email notification class
4. `resources/views/emails/order-confirmation.blade.php` - Email template
5. `database/migrations/2025_06_17_170431_add_razorpay_fields_to_orders_table.php` - Database migration

### Modified Files:
1. `app/Models/Order.php` - Added new payment fields
2. `app/Http/Controllers/OrderController.php` - Updated for Razorpay integration
3. `resources/views/checkout/index.blade.php` - Added payment method selection and Razorpay JS
4. `routes/web.php` - Added payment routes
5. `config/services.php` - Added Razorpay configuration
6. `.env` - Added Razorpay and email settings

## How It Works

### 1. Order Creation Flow
1. Customer fills checkout form and selects payment method
2. Order is created in database with `pending` status
3. For Razorpay payments, a Razorpay order is created via API
4. Payment gateway opens for customer to complete payment

### 2. Payment Verification
1. After payment, Razorpay sends response with payment details
2. Payment signature is verified using Razorpay's verification method
3. Order status is updated to `confirmed` and `paid`
4. Order confirmation email is sent to customer

### 3. Email Notifications
- Responsive HTML email template
- Includes order details, items, pricing, and addresses
- Sent automatically after successful payment
- Queued for better performance

## Testing

### Test Payment Details
Access test payment details at: `/payment/test-details` (only in local environment)

**Test Cards:**
- Success: ****************, CVV: 123, Expiry: 12/25
- Success: ****************, CVV: 123, Expiry: 12/25
- Failure: ****************, CVV: 123, Expiry: 12/25

**Test UPI:** success@razorpay
**Test Net Banking:** HDFC

### Testing Steps:
1. Add products to cart
2. Go to checkout page
3. Fill in shipping/billing details
4. Select "Online Payment" method
5. Click "Proceed to Payment"
6. Use test payment details above
7. Verify order confirmation email is received

## Switching to Production

### 1. Get Production Credentials
1. Sign up at https://razorpay.com
2. Complete KYC verification
3. Get production API keys from dashboard

### 2. Update Environment Variables
Replace test credentials with production ones:
```env
RAZORPAY_KEY_ID=rzp_live_your_key_id
RAZORPAY_KEY_SECRET=your_live_secret_key
RAZORPAY_WEBHOOK_SECRET=your_live_webhook_secret
```

### 3. Configure Webhooks
1. Go to Razorpay Dashboard > Settings > Webhooks
2. Add webhook URL: `https://yourdomain.com/payment/webhook`
3. Select events: `payment.captured`, `payment.failed`, `order.paid`
4. Use the webhook secret in your `.env` file

### 4. Update Email Configuration
Replace Mailtrap with your production email service:
```env
# For Gmail SMTP
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_ENCRYPTION=tls

# Or use services like SendGrid, Mailgun, etc.
```

### 5. Remove Test Routes
Remove or protect the test details route in production:
```php
// In routes/web.php - remove this line in production
Route::get('/payment/test-details', [App\Http\Controllers\PaymentController::class, 'getTestDetails'])->name('payment.test-details');
```

## Security Considerations

1. **API Keys**: Never expose secret keys in frontend code
2. **Signature Verification**: Always verify payment signatures
3. **Webhook Security**: Verify webhook signatures before processing
4. **HTTPS**: Use HTTPS in production for secure communication
5. **Environment Variables**: Keep sensitive data in `.env` file

## Troubleshooting

### Common Issues:

1. **Payment not working**: Check browser console for JavaScript errors
2. **Email not sending**: Verify SMTP credentials and check logs
3. **Webhook failures**: Check webhook URL accessibility and signature verification
4. **Order status not updating**: Check payment verification logic and database updates

### Debug Mode:
Enable debug logging by adding to your `.env`:
```env
LOG_LEVEL=debug
```

Check logs in `storage/logs/laravel.log` for detailed error information.

## Support

For Razorpay-specific issues:
- Documentation: https://razorpay.com/docs/
- Support: https://razorpay.com/support/

For Laravel email issues:
- Documentation: https://laravel.com/docs/mail
- Queue documentation: https://laravel.com/docs/queues

## Next Steps

1. Test the complete flow with test credentials
2. Set up production Razorpay account
3. Configure production email service
4. Set up webhook endpoints
5. Deploy to production environment
6. Monitor payment transactions and email delivery

---

**Note**: This integration uses Razorpay's test environment. All test transactions are simulated and no real money is processed.
