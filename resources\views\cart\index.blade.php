@extends('layouts.app')

@section('title', 'Shopping Cart - ShreeJi Jewelry')
@section('description', 'Review your selected jewelry items and proceed to checkout.')

@section('content')
<!-- Breadcrumb -->
<section class="py-3 bg-light">
    <div class="container">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="{{ route('home') }}">Home</a></li>
                <li class="breadcrumb-item active" aria-current="page">Shopping Cart</li>
            </ol>
        </nav>
    </div>
</section>

<!-- Shopping Cart -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h1 class="font-playfair display-5 fw-bold mb-4">Shopping Cart</h1>
            </div>
        </div>
        
        <div class="row g-5">
            <!-- Cart Items -->
            <div class="col-lg-8">
                <div class="cart-items">
                    @if($cartItems && $cartItems->count() > 0)
                        @foreach($cartItems as $item)
                        <div class="card mb-4 border-0 shadow-sm" id="cart-item-{{ $item->id }}">
                            <div class="card-body p-4">
                                <div class="row align-items-center">
                                    <div class="col-md-2">
                                        <img src="{{ $item->product->main_image_url }}"
                                             alt="{{ $item->product->name }}" class="img-fluid rounded">
                                    </div>
                                    <div class="col-md-4">
                                        <h5 class="font-playfair mb-1">
                                            <a href="{{ route('product.detail', $item->product->slug) }}" class="text-decoration-none text-dark">
                                                {{ $item->product->name }}
                                            </a>
                                        </h5>
                                        <p class="text-muted mb-1">{{ $item->product->category->name }}</p>
                                        <small class="text-muted">
                                            @if($item->size)
                                                Size: {{ $item->size }} |
                                            @endif
                                            SKU: {{ $item->product->sku }}
                                        </small>
                                    </div>
                                    <div class="col-md-2">
                                        <div class="input-group input-group-sm">
                                            <button class="btn btn-outline-secondary" type="button" onclick="updateQuantity({{ $item->id }}, -1)">-</button>
                                            <input type="number" class="form-control text-center" value="{{ $item->quantity }}" min="1" id="qty-{{ $item->id }}" readonly>
                                            <button class="btn btn-outline-secondary" type="button" onclick="updateQuantity({{ $item->id }}, 1)">+</button>
                                        </div>
                                    </div>
                                    <div class="col-md-2 text-center">
                                        @if($item->product->isOnSale())
                                            <span class="h5 text-primary-pink fw-bold">₹{{ number_format($item->product->sale_price * $item->quantity) }}</span>
                                            <br><small class="text-muted text-decoration-line-through">₹{{ number_format($item->product->price * $item->quantity) }}</small>
                                        @else
                                            <span class="h5 text-primary-pink fw-bold">₹{{ number_format($item->product->price * $item->quantity) }}</span>
                                        @endif
                                    </div>
                                    <div class="col-md-2 text-end">
                                        <button class="btn btn-outline-danger btn-sm" onclick="removeItem({{ $item->id }})">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        <button class="btn btn-outline-secondary btn-sm ms-1" onclick="moveToWishlist({{ $item->id }})">
                                            <i class="fas fa-heart"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    @else
                        <!-- Empty Cart -->
                        <div class="text-center py-5">
                            <i class="fas fa-shopping-cart text-muted mb-4" style="font-size: 4rem;"></i>
                            <h3 class="font-playfair mb-3">Your Cart is Empty</h3>
                            <p class="text-muted mb-4">Looks like you haven't added any items to your cart yet.</p>
                            <a href="{{ route('collections') }}" class="btn btn-primary-pink btn-lg">
                                <i class="fas fa-gem me-2"></i>Start Shopping
                            </a>
                        </div>
                    @endif

                    @if($cartItems && $cartItems->count() > 0)
                    <!-- Continue Shopping -->
                    <div class="text-center mt-4">
                        <a href="{{ route('collections') }}" class="btn btn-outline-pink">
                            <i class="fas fa-arrow-left me-2"></i>Continue Shopping
                        </a>
                    </div>
                    @endif
                </div>
            </div>
            
            <!-- Order Summary -->
            <div class="col-lg-4">
                @if($cartItems && $cartItems->count() > 0)
                <div class="card border-0 shadow-sm sticky-top" style="top: 100px;">
                    <div class="card-header bg-gradient-pink text-white">
                        <h5 class="font-playfair mb-0">Order Summary</h5>
                    </div>
                    <div class="card-body p-4">
                        <div class="d-flex justify-content-between mb-2">
                            <span>Subtotal ({{ $cartItems->sum('quantity') }} items)</span>
                            <span id="subtotal">₹{{ number_format($subtotal) }}</span>
                        </div>
                        @if($discount > 0)
                        <div class="d-flex justify-content-between mb-2">
                            <span>Discount</span>
                            <span class="text-success" id="discount">-₹{{ number_format($discount) }}</span>
                        </div>
                        @endif
                        <div class="d-flex justify-content-between mb-2">
                            <span>Shipping</span>
                            @if($subtotal >= 25000)
                                <span class="text-success">Free</span>
                            @else
                                <span>₹500</span>
                            @endif
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span>Tax (GST 3%)</span>
                            <span id="tax">₹{{ number_format($tax) }}</span>
                        </div>
                        <hr>
                        <div class="d-flex justify-content-between mb-3">
                            <strong>Total</strong>
                            <strong class="text-primary-pink h5" id="total">₹{{ number_format($total) }}</strong>
                        </div>

                        <!-- Promo Code -->
                        <div class="mb-3">
                            <div class="input-group">
                                <input type="text" class="form-control" placeholder="Promo code" id="promoCode">
                                <button class="btn btn-outline-secondary" type="button" onclick="applyPromoCode()">Apply</button>
                            </div>
                        </div>

                        <!-- Checkout Button -->
                        <a href="{{ route('checkout') }}" class="btn btn-primary-pink w-100 btn-lg mb-3">
                            <i class="fas fa-lock me-2"></i>Secure Checkout
                        </a>

                        <!-- Payment Methods -->
                        <div class="text-center">
                            <small class="text-muted d-block mb-2">We Accept</small>
                            <div class="d-flex justify-content-center gap-2">
                                <i class="fab fa-cc-visa fs-4 text-primary"></i>
                                <i class="fab fa-cc-mastercard fs-4 text-warning"></i>
                                <i class="fab fa-cc-amex fs-4 text-info"></i>
                                <i class="fab fa-paypal fs-4 text-primary"></i>
                            </div>
                        </div>
                    </div>
                </div>
                @endif
                
                <!-- Security Features -->
                <div class="card border-0 shadow-sm mt-4">
                    <div class="card-body p-4">
                        <h6 class="font-playfair mb-3">Why Shop With Us?</h6>
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-shield-alt text-primary-pink me-2"></i>
                            <small>Secure SSL Encryption</small>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-undo text-primary-pink me-2"></i>
                            <small>30-Day Easy Returns</small>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <i class="fas fa-shipping-fast text-primary-pink me-2"></i>
                            <small>Free Shipping Above ₹25,000</small>
                        </div>
                        <div class="d-flex align-items-center">
                            <i class="fas fa-certificate text-primary-pink me-2"></i>
                            <small>Lifetime Warranty</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Recently Viewed -->
<section class="py-5 bg-light">
    <div class="container">
        <h3 class="font-playfair mb-4">Recently Viewed</h3>
        <div class="row g-4">
            <div class="col-lg-3 col-md-6">
                <div class="card product-card h-100">
                    <div class="position-relative overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1599643478518-a784e5dc4c8f?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80" 
                             class="card-img-top" alt="Necklace">
                        <div class="product-overlay">
                            <a href="#" class="btn btn-light rounded-pill">
                                <i class="fas fa-eye me-2"></i>View Details
                            </a>
                        </div>
                    </div>
                    <div class="card-body text-center">
                        <h5 class="card-title font-playfair">Gold Necklace</h5>
                        <p class="card-text text-muted">22K Yellow Gold</p>
                        <div class="d-flex justify-content-center align-items-center">
                            <span class="text-primary-pink fw-bold">₹32,000</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="card product-card h-100">
                    <div class="position-relative overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1611591437281-460bfbe1220a?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80" 
                             class="card-img-top" alt="Bracelet">
                        <div class="product-overlay">
                            <a href="#" class="btn btn-light rounded-pill">
                                <i class="fas fa-eye me-2"></i>View Details
                            </a>
                        </div>
                    </div>
                    <div class="card-body text-center">
                        <h5 class="card-title font-playfair">Silver Bracelet</h5>
                        <p class="card-text text-muted">Sterling Silver</p>
                        <div class="d-flex justify-content-center align-items-center">
                            <span class="text-primary-pink fw-bold">₹8,500</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="card product-card h-100">
                    <div class="position-relative overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1515562141207-7a88fb7ce338?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80" 
                             class="card-img-top" alt="Ring Set">
                        <div class="product-overlay">
                            <a href="#" class="btn btn-light rounded-pill">
                                <i class="fas fa-eye me-2"></i>View Details
                            </a>
                        </div>
                    </div>
                    <div class="card-body text-center">
                        <h5 class="card-title font-playfair">Wedding Ring Set</h5>
                        <p class="card-text text-muted">18K White Gold</p>
                        <div class="d-flex justify-content-center align-items-center">
                            <span class="text-primary-pink fw-bold">₹55,000</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6">
                <div class="card product-card h-100">
                    <div class="position-relative overflow-hidden">
                        <img src="https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80" 
                             class="card-img-top" alt="Jewelry Set">
                        <div class="product-overlay">
                            <a href="#" class="btn btn-light rounded-pill">
                                <i class="fas fa-eye me-2"></i>View Details
                            </a>
                        </div>
                    </div>
                    <div class="card-body text-center">
                        <h5 class="card-title font-playfair">Bridal Set</h5>
                        <p class="card-text text-muted">22K Gold with Pearls</p>
                        <div class="d-flex justify-content-center align-items-center">
                            <span class="text-primary-pink fw-bold">₹125,000</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection

@push('scripts')
<script>
    function updateQuantity(itemId, change) {
        const qtyInput = document.getElementById(`qty-${itemId}`);
        const currentQty = parseInt(qtyInput.value);
        const newQty = currentQty + change;

        if (newQty >= 1) {
            // Send AJAX request to update quantity
            fetch('/cart/update', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                },
                body: JSON.stringify({
                    item_id: itemId,
                    quantity: newQty
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    qtyInput.value = newQty;
                    updateCartDisplay(data);
                    showNotification('Cart updated successfully', 'success');
                } else {
                    showNotification(data.message || 'Error updating cart', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Error updating cart', 'error');
            });
        }
    }

    function removeItem(itemId) {
        if (confirm('Are you sure you want to remove this item from your cart?')) {
            fetch('/cart/remove', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                },
                body: JSON.stringify({
                    item_id: itemId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById(`cart-item-${itemId}`).remove();
                    updateCartDisplay(data);
                    updateCartCount(data.cart_count);
                    showNotification('Item removed from cart', 'success');

                    // If cart is empty, reload page to show empty state
                    if (data.cart_count === 0) {
                        location.reload();
                    }
                } else {
                    showNotification(data.message || 'Error removing item', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Error removing item', 'error');
            });
        }
    }

    function moveToWishlist(itemId) {
        if (confirm('Move this item to your wishlist?')) {
            fetch('/cart/move-to-wishlist', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                    'Accept': 'application/json',
                },
                body: JSON.stringify({
                    item_id: itemId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById(`cart-item-${itemId}`).remove();
                    updateCartDisplay(data);
                    updateCartCount(data.cart_count);
                    showNotification('Item moved to wishlist', 'success');

                    // If cart is empty, reload page to show empty state
                    if (data.cart_count === 0) {
                        location.reload();
                    }
                } else {
                    showNotification(data.message || 'Please login to use wishlist', 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showNotification('Please login to use wishlist', 'error');
            });
        }
    }

    function applyPromoCode() {
        const promoCode = document.getElementById('promoCode').value.trim();

        if (!promoCode) {
            showNotification('Please enter a promo code', 'error');
            return;
        }

        fetch('/cart/apply-promo', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
                'Accept': 'application/json',
            },
            body: JSON.stringify({
                promo_code: promoCode
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateCartDisplay(data);
                showNotification('Promo code applied successfully!', 'success');
            } else {
                showNotification(data.message || 'Invalid promo code', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('Error applying promo code', 'error');
        });
    }

    function updateCartDisplay(data) {
        // Update totals
        if (data.subtotal !== undefined) {
            document.getElementById('subtotal').textContent = '₹' + data.subtotal.toLocaleString();
        }
        if (data.discount !== undefined) {
            document.getElementById('discount').textContent = '-₹' + data.discount.toLocaleString();
        }
        if (data.tax !== undefined) {
            document.getElementById('tax').textContent = '₹' + data.tax.toLocaleString();
        }
        if (data.total !== undefined) {
            document.getElementById('total').textContent = '₹' + data.total.toLocaleString();
        }
    }

    function updateCartCount(count) {
        const cartCountElements = document.querySelectorAll('.cart-count');
        cartCountElements.forEach(element => {
            element.textContent = count;
        });
    }

    function showNotification(message, type) {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 3000);
    }
</script>
@endpush
