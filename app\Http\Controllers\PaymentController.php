<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\RazorpayService;
use App\Services\TwilioService;
use App\Models\Order;
use App\Models\CartItem;
use App\Mail\OrderConfirmation;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class PaymentController extends Controller
{
    protected $razorpayService;
    protected $twilioService;

    public function __construct(RazorpayService $razorpayService, TwilioService $twilioService)
    {
        $this->razorpayService = $razorpayService;
        $this->twilioService = $twilioService;
    }

    /**
     * Create Razorpay order
     */
    public function createOrder(Request $request)
    {
        try {
            Log::info('Payment createOrder called', ['request' => $request->all()]);

            $order = Order::findOrFail($request->order_id);
            Log::info('Order found', ['order_id' => $order->id, 'user_id' => $order->user_id]);

            // Verify that the order belongs to the authenticated user (skip for testing)
            if (auth()->check() && $order->user_id !== auth()->id()) {
                Log::error('Unauthorized order access', ['order_user_id' => $order->user_id, 'auth_user_id' => auth()->id()]);
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access to order'
                ], 403);
            }

            // Check if order is already paid
            if ($order->payment_status === 'paid') {
                return response()->json([
                    'success' => false,
                    'message' => 'Order is already paid'
                ], 400);
            }

            $result = $this->razorpayService->createOrder($order);
            Log::info('Razorpay service result', ['result' => $result]);

            if ($result['success']) {
                return response()->json([
                    'success' => true,
                    'order_id' => $result['order_id'],
                    'amount' => $result['amount'],
                    'currency' => $result['currency'],
                    'key_id' => $result['key_id'],
                    'customer' => [
                        'name' => $order->user->name,
                        'email' => $order->user->email,
                        'contact' => $order->billing_address['phone'] ?? ''
                    ],
                    'order_number' => $order->order_number
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => $result['message']
                ], 400);
            }

        } catch (ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Order not found'
            ], 404);
        } catch (\Exception $e) {
            Log::error('Payment order creation failed: ' . $e->getMessage());

            // Check if it's a Razorpay authentication error
            $errorMessage = 'Failed to create payment order';
            if (strpos($e->getMessage(), 'Authentication failed') !== false ||
                strpos($e->getMessage(), 'Invalid request parameters') !== false) {
                $errorMessage = 'Payment gateway authentication failed. Please contact support or try again later.';
            }

            return response()->json([
                'success' => false,
                'message' => $errorMessage
            ], 400); // Changed from 500 to 400 for client errors
        }
    }

    /**
     * Verify payment and update order
     */
    public function verifyPayment(Request $request)
    {
        $request->validate([
            'razorpay_order_id' => 'required|string',
            'razorpay_payment_id' => 'required|string',
            'razorpay_signature' => 'required|string',
            'order_id' => 'required|integer'
        ]);

        try {
            DB::beginTransaction();

            $order = Order::findOrFail($request->order_id);

            // Verify that the order belongs to the authenticated user (skip for testing)
            if (auth()->check() && $order->user_id !== auth()->id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access to order'
                ], 403);
            }

            // Verify payment signature
            $verification = $this->razorpayService->verifyPayment(
                $request->razorpay_order_id,
                $request->razorpay_payment_id,
                $request->razorpay_signature
            );

            if ($verification['success']) {
                // Get payment details
                $paymentDetails = $this->razorpayService->getPaymentDetails($request->razorpay_payment_id);

                // Update order with payment information
                $order->update([
                    'payment_status' => 'paid',
                    'status' => 'confirmed',
                    'razorpay_payment_id' => $request->razorpay_payment_id,
                    'razorpay_signature' => $request->razorpay_signature,
                    'payment_transaction_id' => $request->razorpay_payment_id,
                    'payment_details' => $paymentDetails['success'] ? $paymentDetails['payment'] : null,
                    'payment_completed_at' => now(),
                    'confirmed_at' => now()
                ]);

                // Clear cart and update user stats on successful payment
                CartItem::where('user_id', $order->user_id)->delete();
                $order->user->increment('total_spent', $order->total_amount);
                $order->user->addRewardPoints(floor($order->total_amount / 100)); // 1 point per ₹100

                DB::commit();

                // Send order confirmation email and SMS
                try {
                    if ($order->user->email) {
                        Mail::to($order->user->email)->send(new OrderConfirmation($order));
                    }
                } catch (\Exception $e) {
                    Log::error('Failed to send order confirmation email: ' . $e->getMessage());
                }

                // Send order confirmation SMS
                try {
                    if ($order->user->phone) {
                        $this->twilioService->sendOrderConfirmation(
                            $order->user->phone,
                            $order->order_number,
                            $order->user->name
                        );
                    }
                } catch (\Exception $e) {
                    Log::error('Failed to send order confirmation SMS: ' . $e->getMessage());
                }

                return response()->json([
                    'success' => true,
                    'message' => 'Payment verified successfully',
                    'redirect_url' => route('checkout.success', $order->id)
                ]);

            } else {
                DB::rollback();

                // Update order status to failed
                $order->update(['payment_status' => 'failed']);

                // Restore cart items from the failed order
                $this->restoreCartFromOrder($order);

                return response()->json([
                    'success' => false,
                    'message' => 'Payment verification failed. Your cart has been restored.'
                ], 400);
            }

        } catch (ModelNotFoundException $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Order not found'
            ], 404);
        } catch (\Exception $e) {
            DB::rollback();
            Log::error('Payment verification failed: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Payment verification failed'
            ], 500);
        }
    }

    /**
     * Handle payment failure
     */
    public function paymentFailed(Request $request)
    {
        try {
            $order = Order::findOrFail($request->order_id);

            // Verify that the order belongs to the authenticated user (skip for testing)
            if (auth()->check() && $order->user_id !== auth()->id()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unauthorized access to order'
                ], 403);
            }

            // Update order status to failed
            $order->update([
                'payment_status' => 'failed',
                'status' => 'cancelled'
            ]);

            // Restore cart items from the failed order
            $this->restoreCartFromOrder($order);

            return response()->json([
                'success' => false,
                'message' => 'Payment failed. Your cart has been restored.',
                'redirect_url' => route('checkout') . '?payment_failed=1'
            ]);

        } catch (ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Order not found'
            ], 404);
        } catch (\Exception $e) {
            Log::error('Payment failure handling failed: ' . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Failed to process payment failure'
            ], 500);
        }
    }

    /**
     * Webhook handler for Razorpay
     */
    public function webhook(Request $request)
    {
        $payload = $request->getContent();
        $signature = $request->header('X-Razorpay-Signature');

        if (!$this->razorpayService->verifyWebhookSignature($payload, $signature)) {
            Log::error('Invalid webhook signature');
            return response()->json(['error' => 'Invalid signature'], 400);
        }

        $event = json_decode($payload, true);

        try {
            switch ($event['event']) {
                case 'payment.captured':
                    $this->handlePaymentCaptured($event['payload']['payment']['entity']);
                    break;

                case 'payment.failed':
                    $this->handlePaymentFailed($event['payload']['payment']['entity']);
                    break;

                case 'order.paid':
                    $this->handleOrderPaid($event['payload']['order']['entity']);
                    break;

                default:
                    Log::info('Unhandled webhook event: ' . $event['event']);
            }

            return response()->json(['status' => 'success']);

        } catch (\Exception $e) {
            Log::error('Webhook processing failed: ' . $e->getMessage());
            return response()->json(['error' => 'Webhook processing failed'], 500);
        }
    }

    /**
     * Handle payment captured webhook
     */
    private function handlePaymentCaptured($payment)
    {
        $order = Order::where('payment_gateway_order_id', $payment['order_id'])->first();

        if ($order && $order->payment_status !== 'paid') {
            $order->update([
                'payment_status' => 'paid',
                'status' => 'confirmed',
                'razorpay_payment_id' => $payment['id'],
                'payment_transaction_id' => $payment['id'],
                'payment_details' => $payment,
                'payment_completed_at' => now(),
                'confirmed_at' => now()
            ]);

            // Clear cart and update user stats on successful payment
            CartItem::where('user_id', $order->user_id)->delete();
            $order->user->increment('total_spent', $order->total_amount);
            $order->user->addRewardPoints(floor($order->total_amount / 100)); // 1 point per ₹100

            // Send order confirmation email and SMS
            try {
                if ($order->user->email) {
                    Mail::to($order->user->email)->send(new OrderConfirmation($order));
                }
            } catch (\Exception $e) {
                Log::error('Failed to send order confirmation email via webhook: ' . $e->getMessage());
            }

            // Send order confirmation SMS
            try {
                if ($order->user->phone) {
                    $this->twilioService->sendOrderConfirmation(
                        $order->user->phone,
                        $order->order_number,
                        $order->user->name
                    );
                }
            } catch (\Exception $e) {
                Log::error('Failed to send order confirmation SMS via webhook: ' . $e->getMessage());
            }
        }
    }

    /**
     * Handle payment failed webhook
     */
    private function handlePaymentFailed($payment)
    {
        $order = Order::where('payment_gateway_order_id', $payment['order_id'])->first();

        if ($order) {
            $order->update([
                'payment_status' => 'failed',
                'status' => 'cancelled',
                'payment_details' => $payment
            ]);
        }
    }

    /**
     * Handle order paid webhook
     */
    private function handleOrderPaid($orderData)
    {
        $order = Order::where('payment_gateway_order_id', $orderData['id'])->first();

        if ($order && $order->payment_status !== 'paid') {
            $order->update([
                'payment_status' => 'paid',
                'status' => 'confirmed',
                'confirmed_at' => now()
            ]);
        }
    }

    /**
     * Get test payment details for development
     */
    public function getTestDetails()
    {
        if (config('app.env') !== 'local') {
            return response()->json(['error' => 'Not available in production'], 403);
        }

        return response()->json($this->razorpayService->getTestCardDetails());
    }

    /**
     * Restore cart items from a failed order
     */
    private function restoreCartFromOrder($order)
    {
        try {
            // Check if cart is already empty for this user
            $existingCartItems = CartItem::where('user_id', $order->user_id)->count();

            if ($existingCartItems == 0) {
                // Restore cart items from order items
                foreach ($order->orderItems as $orderItem) {
                    CartItem::create([
                        'user_id' => $order->user_id,
                        'product_id' => $orderItem->product_id,
                        'quantity' => $orderItem->quantity,
                        'size' => $orderItem->size,
                        'options' => $orderItem->product_options,
                        'created_at' => now(),
                        'updated_at' => now()
                    ]);
                }

                Log::info("Cart restored for user {$order->user_id} from failed order {$order->id}");
            }
        } catch (\Exception $e) {
            Log::error("Failed to restore cart from order {$order->id}: " . $e->getMessage());
        }
    }
}
